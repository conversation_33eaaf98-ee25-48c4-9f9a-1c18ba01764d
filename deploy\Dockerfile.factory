ARG DOCKER_REGISTRY=harbor.lemonstudio.tech
ARG DOCKER_NAMESPACE=lemon-factory
FROM ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe-builder:latest AS factory

USER frappe
ARG LEMON_FACTORY_BRANCH=develop
ARG LEMON_FACTORY_REPO=ssh://<EMAIL>:29418/lemon-factory
ARG PRINTDESIGN_BRANCH=develop
ARG PRINTDESIGN_REPO=ssh://<EMAIL>:29418/print-designer

# RUN eval $(ssh-agent -s) && \
#     cat /home/<USER>/.ssh/id_rsa | tr -d '\r' | ssh-add - && \
#     cd /home/<USER>/frappe-bench && \
#     bench get-app --branch=${PRINTDESIGN_BRANCH} --resolve-deps print-designer ${PRINTDESIGN_REPO}

# 修正Load key "/home/<USER>/.ssh/id_rsa": error in libcrypto
RUN eval $(ssh-agent -s) && \
    cat /home/<USER>/.ssh/id_rsa | tr -d '\r' | ssh-add - && \
    cd /home/<USER>/frappe-bench && \
    echo 'print-designer' && \
    bench get-app --branch=${PRINTDESIGN_BRANCH} --resolve-deps print-designer ${PRINTDESIGN_REPO} && \
    bench get-app --branch=${LEMON_FACTORY_BRANCH} --resolve-deps lemon-factory ${LEMON_FACTORY_REPO}
  