{"tenant_id": "demo-tenant", "subdomain": "demo-tenant", "docker_registry": "harbor.lemonstudio.tech", "docker_namespace": "lemon-factory", "image_tag": "pro", "domain_suffix": "lemonstudio.tech", "nfs_server": "*************", "nfs_base_path": "/data1/nfs/frappe-lemon", "storage_size": "20Gi", "backend_replicas": 1, "nginx_replicas": 1, "websocket_replicas": 1, "/* 数据库配置选项 */": "", "use_shared_db": true, "shared_db_host": "frappe-mariadb.frappe-lemon.svc.cluster.local", "shared_db_port": "3306", "shared_db_user": "root", "shared_db_password": "mariadb@123LEMON", "site_config": {"admin_password": "admin@321LEMON", "db_name": "frappe", "db_password": "dbadmin@123LEMON", "db_root_password": "mariadb@123LEMON", "install_apps": "erpnext,lemon_factory,print_designer"}, "use_cert_manager": false}