apiVersion: batch/v1
kind: Job
metadata:
  name: ${TENANT_ID}-nfs-init
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-nfs-init
    tenant: ${TENANT_ID}
spec:
  ttlSecondsAfterFinished: 100
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: ${TENANT_ID}-nfs-init
        tenant: ${TENANT_ID}
    spec:
      restartPolicy: Never
      imagePullSecrets:
      - name: harborsecret
      containers:
      - name: nfs-init
        image: busybox:1.36
        command: ["/bin/sh", "-c"]
        args:
          - |
            echo "Creating NFS directories for tenant: ${TENANT_ID}..."
            mkdir -p ${NFS_BASE_PATH}/${TENANT_ID}/sites
            mkdir -p ${NFS_BASE_PATH}/${TENANT_ID}/assets
            mkdir -p ${NFS_BASE_PATH}/${TENANT_ID}/logs
            mkdir -p ${NFS_BASE_PATH}/${TENANT_ID}/mariadb
            echo "Setting proper permissions..."
            chmod -R 777 ${NFS_BASE_PATH}/${TENANT_ID}
            echo "NFS directories created successfully."
            
            # 尝试挂载目录来验证我们是否有权限访问
            echo "Testing NFS mount points..."
            mount -t nfs ${NFS_SERVER}:${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}/sites /tmp/${TENANT_ID}-sites || true
            mount -t nfs ${NFS_SERVER}:${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}/assets /tmp/${TENANT_ID}-assets || true
            mount -t nfs ${NFS_SERVER}:${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}/logs /tmp/${TENANT_ID}-logs || true
            
            # 测试权限
            if touch /tmp/${TENANT_ID}-sites/test.txt 2>/dev/null; then
              echo "Sites directory is writable"
              rm /tmp/${TENANT_ID}-sites/test.txt
            else
              echo "WARNING: Cannot write to sites directory!"
            fi
            
            if touch /tmp/${TENANT_ID}-assets/test.txt 2>/dev/null; then
              echo "Assets directory is writable"
              rm /tmp/${TENANT_ID}-assets/test.txt
            else
              echo "WARNING: Cannot write to assets directory!"
            fi
            
            if touch /tmp/${TENANT_ID}-logs/test.txt 2>/dev/null; then
              echo "Logs directory is writable"
              rm /tmp/${TENANT_ID}-logs/test.txt
            else
              echo "WARNING: Cannot write to logs directory!"
            fi
            
            # 卸载测试目录
            umount /tmp/${TENANT_ID}-sites 2>/dev/null || true
            umount /tmp/${TENANT_ID}-assets 2>/dev/null || true
            umount /tmp/${TENANT_ID}-logs 2>/dev/null || true
            
            echo "Please ensure the following directories exist on the NFS server with proper permissions:"
            echo "${NFS_SERVER}:${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}/sites"
            echo "${NFS_SERVER}:${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}/assets"
            echo "${NFS_SERVER}:${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}/logs"
            
            echo "NFS目录结构创建完成!"
        securityContext:
          privileged: true
