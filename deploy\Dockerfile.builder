ARG DOCKER_REGISTRY=harbor.lemonstudio.tech
ARG DOCKER_NAMESPACE=lemon-factory
FROM ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe-build:latest AS builder

ARG FRAPPE_BRANCH=develop
ARG FRAPPE_TAG=""
ARG FRAPPE_COMMIT=""
ARG FRAPPE_REPO=https://github.com/frappe/frappe
ARG ERPNEXT_BRANCH=develop
ARG ERPNEXT_TAG=""
ARG ERPNEXT_COMMIT=""
ARG ERPNEXT_REPO=https://github.com/frappe/erpnext
ARG HRMS_BRANCH=develop
ARG HRMS_TAG=""
ARG HRMS_COMMIT=""
ARG HRMS_REPO=https://github.com/frappe/hrms


USER root

COPY resources/.ssh /home/<USER>/.ssh
COPY resources/docker_config.json /home/<USER>/.docker/config.json

RUN chown -R frappe:frappe /home/<USER>/.ssh && chmod 0600 /home/<USER>/.ssh/id_rsa

RUN echo '************** gerrit.lemonstudio.tech' >> /etc/hosts

USER frappe
RUN \
  # 设置镜像
  mkdir ~/.pip \
  && echo '[global]' > ~/.pip/pip.conf \
  && echo 'index-url = https://mirrors.aliyun.com/pypi/simple/' >> ~/.pip/pip.conf \
  && echo "trusted-host = mirrors.aliyun.com" >> ~/.pip/pip.conf \
  # 初始化bench，根据是否有commit、tag或branch决定使用哪种方式
  && if [ -n "${FRAPPE_COMMIT}" ]; then \
    # For commit, we first create a temporary clone
    mkdir -p /tmp/frappe && \
    git clone ${FRAPPE_REPO} /tmp/frappe && \
    cd /tmp/frappe && \
    git fetch --all && \
    git checkout ${FRAPPE_COMMIT} && \
    # Now use the local path for frappe
    bench init \
    --frappe-path=/tmp/frappe \
    --no-procfile \
    --no-backups \
    --skip-redis-config-generation \
    --verbose \
    /home/<USER>/frappe-bench; \
  elif [ -n "${FRAPPE_TAG}" ]; then \
    # For tag, we first create a temporary clone
    mkdir -p /tmp/frappe && \
    git clone ${FRAPPE_REPO} /tmp/frappe && \
    cd /tmp/frappe && \
    git fetch --all --tags && \
    git checkout tags/${FRAPPE_TAG} -b ${FRAPPE_TAG}-branch && \
    # Now use the local path for frappe
    bench init \
    --frappe-path=/tmp/frappe \
    --no-procfile \
    --no-backups \
    --skip-redis-config-generation \
    --verbose \
    /home/<USER>/frappe-bench; \
  else \
    bench init \
    --frappe-path=${FRAPPE_REPO} \
    --frappe-branch=${FRAPPE_BRANCH} \
    --no-procfile \
    --no-backups \
    --skip-redis-config-generation \
    --verbose \
    /home/<USER>/frappe-bench; \
  fi \
  && cd /home/<USER>/frappe-bench \
  # 根据是否有commit、tag或branch决定安装ERPNext时使用哪种方式
  && if [ -n "${ERPNEXT_COMMIT}" ]; then \
    mkdir -p /tmp/erpnext && \
    git clone ${ERPNEXT_REPO} /tmp/erpnext && \
    cd /tmp/erpnext && \
    git fetch --all && \
    git checkout ${ERPNEXT_COMMIT} && \
    cd /home/<USER>/frappe-bench && \
    bench get-app --resolve-deps erpnext /tmp/erpnext; \
  elif [ -n "${ERPNEXT_TAG}" ]; then \
    mkdir -p /tmp/erpnext && \
    git clone ${ERPNEXT_REPO} /tmp/erpnext && \
    cd /tmp/erpnext && \
    git fetch --all --tags && \
    git checkout tags/${ERPNEXT_TAG} -b ${ERPNEXT_TAG}-branch && \
    cd /home/<USER>/frappe-bench && \
    bench get-app --resolve-deps erpnext /tmp/erpnext; \
  else \
    bench get-app --branch=${ERPNEXT_BRANCH} --resolve-deps erpnext ${ERPNEXT_REPO}; \
  fi \
  # 根据是否有commit、tag或branch决定安装HRMS时使用哪种方式
  && if [ -n "${HRMS_COMMIT}" ]; then \
    mkdir -p /tmp/hrms && \
    git clone ${HRMS_REPO} /tmp/hrms && \
    cd /tmp/hrms && \
    git fetch --all && \
    git checkout ${HRMS_COMMIT} && \
    cd /home/<USER>/frappe-bench && \
    bench get-app --resolve-deps hrms /tmp/hrms; \
  elif [ -n "${HRMS_TAG}" ]; then \
    mkdir -p /tmp/hrms && \
    git clone ${HRMS_REPO} /tmp/hrms && \
    cd /tmp/hrms && \
    git fetch --all --tags && \
    git checkout tags/${HRMS_TAG} -b ${HRMS_TAG}-branch && \
    cd /home/<USER>/frappe-bench && \
    bench get-app --resolve-deps hrms /tmp/hrms; \
  else \
    bench get-app --branch=${HRMS_BRANCH} --resolve-deps hrms ${HRMS_REPO}; \
  fi \
  && echo "{}" > sites/common_site_config.json \
  && find apps -mindepth 1 -path "*/.git" | xargs rm -fr
