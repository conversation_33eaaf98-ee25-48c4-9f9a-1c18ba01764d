apiVersion: v1
kind: Service
metadata:
  name: ${TENANT_ID}-frappe
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-frappe
    tenant: ${TENANT_ID}
spec:
  selector:
    app: ${TENANT_ID}-frappe
    tenant: ${TENANT_ID}
  ports:
    - port: 8080
      targetPort: 8080
      name: http
    - port: 8000
      targetPort: 8000
      name: backend
    - port: 9000
      targetPort: 9000
      name: websocket
    - port: 3306
      targetPort: 3306
      name: mariadb
    - port: 6379
      targetPort: 6379
      name: redis
