#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Frappe-Lemon 多租户部署脚本 (Python版)

此脚本用于生成特定租户的Kubernetes配置文件，并可以选择性地配置阿里云DNS。
"""

import os
import sys
import argparse
import subprocess
import json
import base64
import hmac
import hashlib
import time
import urllib.parse
import urllib.request
from string import Template
from datetime import datetime
import requests
from pathlib import Path

# todo: 配置时区
# ANSI 颜色代码
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    CYAN = '\033[0;36m'
    GRAY = '\033[0;37m'
    NC = '\033[0m'  # 无颜色


def print_colored(message, color=""):
    """打印彩色文本"""
    color_code = getattr(Colors, color.upper(), "")
    print(f"{color_code}{message}{Colors.NC}")


class TenantDeployer:
    """
    租户部署器类，负责处理租户部署的所有逻辑
    """
    
    def __init__(self):
        """初始化默认值和参数解析"""
        # 设置默认值
        self.tenant_id = ""
        self.subdomain = ""
        self.docker_registry = "harbor.lemonstudio.tech"
        self.docker_namespace = "lemon-factory"
        self.image_tag = "latest"
        self.docker_config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources/docker_config.json")
        self.domain_suffix = "lemonstudio.tech"
        self.storage_class_name = "standard"
        self.backend_replicas = 1
        self.nginx_replicas = 1
        self.websocket_replicas = 1
        self.total_storage_size = "20Gi"
        self.ingress_class = "nginx"
        self.max_body_size = "50m"
        
        # 部署模式：'separate'(默认，多个Pod)或'all-in-one'(单个Pod)
        self.deployment_mode = "all-in-one"
        
        # 存储配置
        self.nfs_server = "172.24.187.107"
        self.nfs_base_path = "/data0/nfs"
        
        # 站点初始化配置
        self.admin_password = "admin"
        self.db_name = "frappe"
        self.db_password = "frappe"
        self.db_root_password = "mariadb"
        self.install_apps = "erpnext,lemon_factory"
        self.initialize_site = True
        
        # 阿里云DNS配置
        self.alicloud_enable = False
        self.alicloud_access_key_id = ""
        self.alicloud_access_key_secret = ""
        self.alicloud_region = "cn-hangzhou"
        self.ingress_ip = ""
        
        # 脚本位置和路径
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.templates_dir = os.path.join(self.script_dir, "templates")
        
        # 解析命令行参数
        self.parse_arguments()
        
    def load_config_file(self, config_file):
        """从JSON配置文件加载配置"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                print_colored(f"错误: 配置文件不存在: {config_file}", "RED")
                sys.exit(1)
                
            print_colored(f"从配置文件加载: {config_file}", "CYAN")
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 更新基本配置
            for key in [
                "tenant_id", "subdomain", "docker_registry", "docker_namespace", 
                "image_tag", "docker_config_path", "domain_suffix", "backend_replicas",
                "nginx_replicas", "websocket_replicas", "storage",
                "ingress_class", "max_body_size",
                "deployment_mode", "nfs_server", "nfs_base_path"
            ]:
                if key in config and config[key]:
                    setattr(self, key, config[key])
            
            # 更新站点配置
            if "site_config" in config and isinstance(config["site_config"], dict):
                site_config = config["site_config"]
                if "admin_password" in site_config:
                    self.admin_password = site_config["admin_password"]
                if "db_name" in site_config:
                    self.db_name = site_config["db_name"]
                if "db_password" in site_config:
                    self.db_password = site_config["db_password"]
                if "db_root_password" in site_config:
                    self.db_root_password = site_config["db_root_password"]
                if "install_apps" in site_config:
                    self.install_apps = site_config["install_apps"]
                if "initialize" in site_config:
                    self.initialize_site = site_config["initialize"]
                    
            # 更新阿里云DNS配置
            if "alicloud_dns" in config and isinstance(config["alicloud_dns"], dict):
                dns_config = config["alicloud_dns"]
                if "enable" in dns_config:
                    self.alicloud_enable = dns_config["enable"]
                if "access_key_id" in dns_config:
                    self.alicloud_access_key_id = dns_config["access_key_id"]
                if "access_key_secret" in dns_config:
                    self.alicloud_access_key_secret = dns_config["access_key_secret"]
                if "region" in dns_config:
                    self.alicloud_region = dns_config["region"]
                if "ingress_ip" in dns_config:
                    self.ingress_ip = dns_config["ingress_ip"]
                    
            print_colored("配置文件加载成功", "GREEN")
            return True
        except json.JSONDecodeError:
            print_colored(f"错误: 配置文件格式不正确: {config_file}", "RED")
            sys.exit(1)
        except Exception as e:
            print_colored(f"加载配置文件时出错: {str(e)}", "RED")
            sys.exit(1)
    
    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description="Frappe-Lemon 多租户部署脚本")
        
        # 配置文件参数
        parser.add_argument("--config", "-c", help="JSON配置文件路径")
        
        # 必选参数
        parser.add_argument("--tenant-id", help="租户ID（必需）")
        
        # 可选参数
        parser.add_argument("--subdomain", help="自定义二级域名（默认：使用租户ID）")
        parser.add_argument("--docker-registry", help=f"Docker 仓库 URL（默认：{self.docker_registry}）")
        parser.add_argument("--docker-namespace", help=f"Docker 命名空间（默认：{self.docker_namespace}）")
        parser.add_argument("--image-tag", help=f"镜像标签（默认：{self.image_tag}）")
        parser.add_argument("--docker-config", help="Docker 配置文件路径（默认：{self.docker_config_path}）")
        parser.add_argument("--domain-suffix", help=f"域名后缀（默认：{self.domain_suffix}）")
        parser.add_argument("--nfs-server", help=f"NFS服务器地址（默认：{self.nfs_server}）")
        parser.add_argument("--nfs-base-path", help=f"NFS基础路径（默认：{self.nfs_base_path}）")
        parser.add_argument("--backend-replicas", type=int, help=f"后端副本数（默认：{self.backend_replicas}）")
        parser.add_argument("--nginx-replicas", type=int, help=f"Nginx 副本数（默认：{self.nginx_replicas}）")
        parser.add_argument("--websocket-replicas", type=int, help=f"WebSocket 副本数（默认：{self.websocket_replicas}）")
        parser.add_argument("--total-storage", help=f"总存储大小（默认：{self.total_storage_size}）")
        parser.add_argument("--ingress-class", help=f"Ingress 类（默认：{self.ingress_class}）")
        parser.add_argument("--max-body-size", help=f"Ingress 最大请求体大小（默认：{self.max_body_size}）")
        parser.add_argument("--deployment-mode", choices=["separate", "all-in-one"], 
                          help=f"部署模式，'separate'（默认，多个Pod）或'all-in-one'（单个Pod）")
        
        # 站点初始化参数
        parser.add_argument("--admin-password", help=f"管理员密码（默认：{self.admin_password}）")
        parser.add_argument("--db-name", help=f"数据库名称（默认：{self.db_name}）")
        parser.add_argument("--db-password", help=f"数据库密码（默认：{self.db_password}）")
        parser.add_argument("--db-root-password", help=f"数据库root密码（默认：{self.db_root_password}）")
        parser.add_argument("--install-apps", help=f"逗号分隔的应用列表（默认：{self.install_apps}）")
        parser.add_argument("--initialize-site", action="store_true", help="是否初始化站点")
        parser.add_argument("--no-initialize-site", action="store_true", help="不初始化站点")
        
        # 阿里云DNS参数
        parser.add_argument("--aliyun-dns", action="store_true", help="启用阿里云DNS自动配置")
        parser.add_argument("--access-key-id", help="阿里云AccessKey ID")
        parser.add_argument("--access-key-secret", help="阿里云AccessKey Secret")
        parser.add_argument("--aliyun-region", help=f"阿里云区域（默认：{self.alicloud_region}）")
        parser.add_argument("--ingress-ip", help="Ingress控制器的外部IP地址")
        
        args = parser.parse_args()
        
        # 先从配置文件加载（如果提供）
        if args.config:
            self.load_config_file(args.config)
            
        # 命令行参数覆盖配置文件
        # 设置参数值（只有在命令行中提供的参数才会覆盖默认值或配置文件值）
        if args.tenant_id:
            self.tenant_id = args.tenant_id
        if args.subdomain:
            self.subdomain = args.subdomain
        elif not self.subdomain and self.tenant_id:
            self.subdomain = self.tenant_id
        if args.docker_registry:
            self.docker_registry = args.docker_registry
        if args.docker_namespace:
            self.docker_namespace = args.docker_namespace
        if args.image_tag:
            self.image_tag = args.image_tag
        if args.docker_config:
            self.docker_config_path = args.docker_config
        if args.domain_suffix:
            self.domain_suffix = args.domain_suffix
        if args.nfs_server:
            self.nfs_server = args.nfs_server
        if args.nfs_base_path:
            self.nfs_base_path = args.nfs_base_path
        if args.backend_replicas is not None:
            self.backend_replicas = args.backend_replicas
        if args.nginx_replicas is not None:
            self.nginx_replicas = args.nginx_replicas
        if args.websocket_replicas is not None:
            self.websocket_replicas = args.websocket_replicas
        if args.total_storage:
            self.total_storage_size = args.total_storage
        if args.ingress_class:
            self.ingress_class = args.ingress_class
        if args.max_body_size:
            self.max_body_size = args.max_body_size
        if args.deployment_mode:
            self.deployment_mode = args.deployment_mode
        
        # 站点初始化参数
        if args.admin_password:
            self.admin_password = args.admin_password
        if args.db_name:
            self.db_name = args.db_name
        if args.db_password:
            self.db_password = args.db_password
        if args.db_root_password:
            self.db_root_password = args.db_root_password
        if args.install_apps:
            self.install_apps = args.install_apps
        if args.initialize_site:
            self.initialize_site = True
        if args.no_initialize_site:
            self.initialize_site = False
            
        # 阿里云DNS配置（命令行参数优先）
        if args.aliyun_dns:
            self.alicloud_enable = True
        if args.access_key_id:
            self.alicloud_access_key_id = args.access_key_id
        if args.access_key_secret:
            self.alicloud_access_key_secret = args.access_key_secret
        if args.aliyun_region:
            self.alicloud_region = args.aliyun_region
        if args.ingress_ip:
            self.ingress_ip = args.ingress_ip
        
        # 输出目录
        self.output_dir = os.path.join(self.script_dir, "tenants", self.tenant_id)
        
        # 参数验证
        self.validate_parameters()
        
    def validate_parameters(self):
        """验证参数有效性"""
        # 验证租户ID
        if not self.tenant_id:
            print_colored("错误: 租户ID是必需的，请通过--tenant-id参数或配置文件提供", "RED")
            sys.exit(1)
        
        # 阿里云DNS验证
        if self.alicloud_enable:
            if not self.alicloud_access_key_id or not self.alicloud_access_key_secret:
                print_colored("错误: 启用阿里云DNS时，必需提供AccessKey ID和AccessKey Secret", "RED")
                sys.exit(1)
            if not self.ingress_ip:
                print_colored("错误: 启用阿里云DNS时，必需提供Ingress控制器的外部IP", "RED")
                sys.exit(1)
        
        # 如果没有指定二级域名，使用租户ID
        if not self.subdomain:
            self.subdomain = self.tenant_id
            print_colored(f"使用租户ID '{self.tenant_id}' 作为二级域名", "CYAN")
        else:
            print_colored(f"使用自定义二级域名: '{self.subdomain}'", "CYAN")
    
    def create_output_directory(self):
        """创建输出目录"""
        os.makedirs(self.output_dir, exist_ok=True)
        print_colored(f"已创建输出目录: {self.output_dir}", "CYAN")
    
    def run_command(self, command, shell=True):
        """运行命令并返回结果"""
        try:
            result = subprocess.run(command, shell=shell, check=True, 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            print_colored(f"执行命令失败: {e}", "RED")
            print_colored(f"错误输出: {e.stderr}", "RED")
            return None
    
    def check_namespace_exists(self):
        """检查命名空间是否存在"""
        try:
            result = subprocess.run(["kubectl", "get", "namespace", "frappe-lemon"], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            return result.returncode == 0
        except:
            return False
    
    def ensure_namespace_exists(self):
        """确保命名空间存在"""
        # 检查命名空间是否存在
        if not self.check_namespace_exists():
            print_colored("警告: frappe-lemon 命名空间不存在", "YELLOW")
            print_colored("请先创建命名空间: kubectl create namespace frappe-lemon", "YELLOW")
            sys.exit(1)
        else:
            print_colored("frappe-lemon 命名空间已存在", "CYAN")
    
    def process_template(self, template_file, output_file):
        """处理模板文件，替换变量"""
        if not os.path.exists(template_file):
            print_colored(f"错误: 模板文件不存在: {template_file}", "RED")
            return False
        
        template_name = os.path.basename(template_file)
        print_colored(f"处理模板: {template_name}", "GRAY")
        
        # 创建替换变量字典
        variables = {
            "TENANT_ID": self.tenant_id,
            "SUBDOMAIN": self.subdomain,
            "DOCKER_REGISTRY": self.docker_registry,
            "DOCKER_NAMESPACE": self.docker_namespace,
            "IMAGE_TAG": self.image_tag,
            "DOCKER_CONFIG_JSON": self.generate_docker_config_json(),
            "DOMAIN_SUFFIX": self.domain_suffix,
            "NFS_SERVER": self.nfs_server,
            "NFS_BASE_PATH": self.nfs_base_path,
            "BACKEND_REPLICAS": str(self.backend_replicas),
            "NGINX_REPLICAS": str(self.nginx_replicas),
            "WEBSOCKET_REPLICAS": str(self.websocket_replicas),
            "TOTAL_STORAGE_SIZE": self.total_storage_size,
            "STORAGE_CLASS_NAME": self.storage_class_name,
            "INGRESS_CLASS": self.ingress_class,
            "MAX_BODY_SIZE": self.max_body_size,
            "ADMIN_PASSWORD": self.admin_password,
            "DB_NAME": self.db_name,
            "DB_PASSWORD": self.db_password,
            "DB_ROOT_PASSWORD": self.db_root_password,
            "INSTALL_APPS": self.install_apps,
            "INITIALIZE_SITE": str(self.initialize_site).lower()
        }
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 使用字符串模板替换变量
            # 首先替换环境变量风格 ${VAR}
            for key, value in variables.items():
                template_content = template_content.replace(f"${{{key}}}", value)
            
            # 然后再处理其他带默认值的情况，例如 ${VAR:-default}
            import re
            def replace_with_default(match):
                var_name, default = match.group(1).split(":-")
                return variables.get(var_name, default)
                
            template_content = re.sub(r'\${([^}]+)}', lambda m: replace_with_default(m) if ":-" in m.group(1) else m.group(0), template_content)
            
            # 写入输出文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            print_colored(f"成功处理模板: {template_name}", "GREEN")
            return True
        except Exception as e:
            print_colored(f"处理模板失败: {template_name}, 错误: {str(e)}", "RED")
            return False
    
    def generate_docker_config_json(self):
        """从配置文件加载Docker配置JSON"""
        try:
            # 检查Docker配置文件存在
            if not os.path.exists(self.docker_config_path):
                print_colored(f"警告: Docker配置文件不存在: {self.docker_config_path}", "YELLOW")
                return ""
            
            print_colored(f"从文件加载Docker配置: {self.docker_config_path}", "CYAN")
            
            # 读取Docker配置文件
            with open(self.docker_config_path, 'r') as f:
                config_content = f.read()
            
            # 将整个JSON进行Base64编码
            config_bytes = config_content.encode('ascii')
            base64_config = base64.b64encode(config_bytes).decode('ascii')
            
            return base64_config
            
        except Exception as e:
            print_colored(f"读取Docker配置文件时出错: {str(e)}", "RED")
            return ""
    
    def process_all_templates(self):
        """处理所有模板文件"""
        if self.deployment_mode == "all-in-one":
            print_colored("使用单Pod部署模式...", "CYAN")
            # 处理单Pod模板
            template_files = [
                "docker-registry-secret.yaml", "data-pvc.yaml", "configmap.yaml",
                "all-in-one-deployment.yaml", "all-in-one-service.yaml", "ingress.yaml"
            ]
            
            # 如果需要初始化站点，添加初始化作业
            if self.initialize_site:
                template_files.append("site-init-job.yaml")
                print_colored("已添加站点初始化作业...", "YELLOW")
            for template_file in template_files:
                template_path = os.path.join(self.templates_dir, template_file)
                output_path = os.path.join(self.output_dir, template_file)
                if not os.path.exists(template_path):
                    print_colored(f"警告: 模板文件不存在: {template_file}", "YELLOW")
                    continue
                self.process_template(template_path, output_path)
        else:
            print_colored("使用多Pod部署模式...", "CYAN")
            # 处理多Pod模板
            for template_file in os.listdir(self.templates_dir):
                if template_file.endswith(".yaml") and "all-in-one" not in template_file and template_file != "namespace.yaml":
                    template_path = os.path.join(self.templates_dir, template_file)
                    output_path = os.path.join(self.output_dir, template_file)
                    self.process_template(template_path, output_path)
    
    def create_convenience_scripts(self):
        """创建便捷脚本"""
        # 创建apply.sh脚本
        apply_script = os.path.join(self.output_dir, "apply.sh")
        with open(apply_script, 'w', encoding='utf-8') as f:
            f.write(f"""#!/bin/bash
# 为租户应用所有 Kubernetes 清单: {self.tenant_id}
kubectl apply -f {self.output_dir}
""")
        # 设置执行权限
        os.chmod(apply_script, 0o755)
        print_colored(f"创建了便捷脚本: {apply_script}", "GREEN")
        
        # 创建delete.sh脚本
        delete_script = os.path.join(self.output_dir, "delete.sh")
        with open(delete_script, 'w', encoding='utf-8') as f:
            f.write(f"""#!/bin/bash
# 删除租户的所有 Kubernetes 资源: {self.tenant_id}
kubectl delete -f {self.output_dir}
""")
        # 设置执行权限
        os.chmod(delete_script, 0o755)
        print_colored(f"创建了便捷脚本: {delete_script}", "GREEN")

    def configure_aliyun_dns(self):
        """配置阿里云DNS记录"""
        if not self.alicloud_enable:
            return
        
        print_colored("开始配置阿里云DNS记录...", "CYAN")
        
        # 提取主域名
        domain_parts = self.domain_suffix.split('.')
        if len(domain_parts) >= 2:
            main_domain = '.'.join(domain_parts[-2:])
        else:
            main_domain = self.domain_suffix
        
        # 配置DNS记录
        success = self.add_dns_record(main_domain, self.subdomain, self.ingress_ip)
        
        if success:
            print_colored(f"阿里云DNS记录配置成功: {self.subdomain}.{self.domain_suffix} -> {self.ingress_ip}", "GREEN")
        else:
            print_colored(f"警告: 阿里云DNS记录配置失败，请手动配置域名解析", "YELLOW")
    
    def get_timestamp(self):
        """获取ISO8601格式的UTC时间戳"""
        # 不在URL编码时间戳中预先处理特殊字符(如冒号)
        # 而是返回原始格式，让urllib.parse.quote在构建查询字符串时处理它
        return datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")
    
    def get_nonce(self):
        """获取随机数作为SignatureNonce"""
        return str(int(time.time() * 1000))
    
    def compute_signature(self, string_to_sign, secret):
        """计算签名 (HMAC-SHA1)"""
        key = secret + "&"
        message = string_to_sign.encode('utf-8')
        hmac_code = hmac.new(key.encode('utf-8'), message, hashlib.sha1).digest()
        return base64.b64encode(hmac_code).decode('utf-8')
    
    def add_dns_record(self, domain, rr, value):
        """添加或更新阿里云DNS记录"""
        try:
            # 首先查询是否已存在记录
            record_id = self.get_domain_record_id(domain, rr)
            
            # 构建基本参数
            params = {
                "AccessKeyId": self.alicloud_access_key_id,
                "Format": "JSON",
                "SignatureMethod": "HMAC-SHA1",
                "SignatureNonce": self.get_nonce(),
                "SignatureVersion": "1.0",
                "Timestamp": self.get_timestamp(),
                "Version": "2015-01-09"
            }
            
            # 根据是否存在记录决定添加还是更新
            if record_id:
                print_colored(f"DNS记录已存在 (ID: {record_id})，更新记录", "YELLOW")
                action = "UpdateDomainRecord"
                params["Action"] = action
                params["RecordId"] = record_id
            else:
                print_colored("DNS记录不存在，创建新记录", "YELLOW")
                action = "AddDomainRecord"
                params["Action"] = action
                params["DomainName"] = domain
            
            # 添加共同参数
            params["RR"] = rr
            params["Type"] = "A"
            params["Value"] = value
            
            # 签名并发送请求
            sorted_params = dict(sorted(params.items()))
            query_string = "&".join([f"{urllib.parse.quote(k)}={urllib.parse.quote(str(v))}" for k, v in sorted_params.items()])
            
            string_to_sign = f"GET&%2F&{urllib.parse.quote(query_string)}"
            signature = self.compute_signature(string_to_sign, self.alicloud_access_key_secret)
            
            url = f"http://alidns.aliyuncs.com/?{query_string}&Signature={urllib.parse.quote(signature)}"
            
            response = requests.get(url)
            result = response.json()
            
            # 检查是否成功
            if "RecordId" in result:
                return True
            else:
                print_colored(f"DNS操作失败: {result}", "RED")
                return False
                
        except Exception as e:
            print_colored(f"配置DNS时出错: {str(e)}", "RED")
            return False
    
    def get_domain_record_id(self, domain, rr):
        """获取特定域名记录的ID"""
        try:
            # 构建基本参数
            params = {
                "AccessKeyId": self.alicloud_access_key_id,
                "Action": "DescribeDomainRecords",
                "DomainName": domain,
                "Format": "JSON",
                "RRKeyWord": rr,
                "SignatureMethod": "HMAC-SHA1",
                "SignatureNonce": self.get_nonce(),
                "SignatureVersion": "1.0",
                "Timestamp": self.get_timestamp(),
                "Version": "2015-01-09"
            }
            
            # 签名并发送请求
            sorted_params = dict(sorted(params.items()))
            query_string = "&".join([f"{urllib.parse.quote(k)}={urllib.parse.quote(str(v))}" for k, v in sorted_params.items()])
            
            string_to_sign = f"GET&%2F&{urllib.parse.quote(query_string)}"
            signature = self.compute_signature(string_to_sign, self.alicloud_access_key_secret)
            
            url = f"http://alidns.aliyuncs.com/?{query_string}&Signature={urllib.parse.quote(signature)}"
            
            response = requests.get(url)
            result = response.json()
            
            # 提取记录ID
            records = result.get("DomainRecords", {}).get("Record", [])
            for record in records:
                if record.get("RR") == rr and record.get("Type") == "A":
                    return record.get("RecordId")
            
            return None
                
        except Exception as e:
            print_colored(f"获取DNS记录ID时出错: {str(e)}", "RED")
            return None
    
    def run(self):
        """运行部署过程"""
        print_colored(f"正在为租户生成 Kubernetes 清单: {self.tenant_id}", "CYAN")
        
        # 创建输出目录
        self.create_output_directory()
        
        # 确保命名空间存在
        self.ensure_namespace_exists()
        
        # 处理所有模板
        self.process_all_templates()
        
        # 创建便捷脚本
        self.create_convenience_scripts()
        
        # 配置阿里云DNS (如果启用)
        if self.alicloud_enable:
            self.configure_aliyun_dns()
        
        print_colored(f"所有 Kubernetes 清单生成成功，位于 {self.output_dir}", "GREEN")
        print_colored(f"1. kubectl apply -f {self.output_dir}/data-pvc.yaml", "YELLOW")
        print_colored(f"2. kubectl apply -f {self.output_dir}", "YELLOW")


if __name__ == "__main__":
    deployer = TenantDeployer()
    deployer.run()
