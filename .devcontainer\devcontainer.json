{
  "name": "frappe-devcontainer",
  "forwardPorts": [8000, 9000, 6787],
  "remoteUser": "frappe",
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-python.python",
        "ms-vscode.live-server",
        "grapecity.gc-excelviewer",
        "mtxr.sqltools",
        "visualstudioexptteam.vscodeintellicode"
      ],
      "settings": {
        "terminal.integrated.profiles.linux": {
          "frappe bash": {
            "path": "/bin/bash"
          }
        },
        "terminal.integrated.defaultProfile.linux": "frappe bash",
        "debug.node.autoAttach": "disabled"
      }
    }
  },
  "dockerComposeFile": "./docker-compose.yml",
  "service": "frappe",
  "workspaceFolder": "/workspace/development",
  "shutdownAction": "stopCompose",
  // "mounts": [
  //   "source=${localEnv:HOME}${localEnv:USERPROFILE}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached"
  // ],
  "remoteEnv": {
        "DOCKER_HOST": "tcp://host.docker.internal:2375",
        "NODEJS_ORG_MIRROR": "https://npmmirror.com/mirrors/node/"
  },
  "postCreateCommand": "/workspace/.devcontainer/post-create.sh"
}
