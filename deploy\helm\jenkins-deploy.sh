#!/bin/bash

# 脚本有任意错误直接退出
set -e

# 参数
tenant_id=$1
config_json=$2
deploy_type=$3
tag=$4

# 切换到helm目录
cd /data0/frappe_lemon/deploy/helm


if [ "$deploy_type" = "install" ]; then
    # 切换到部署目录并构建镜像
    cd /data0/frappe_lemon/deploy
    ./build.sh --image-type=factory --push-images --no-build-cache --tag=$tag
    ./build.sh --image-type=runtime --push-images --no-build-cache --tag=$tag
    # 切回helm目录
    cd /data0/frappe_lemon/deploy/helm
fi

# 启用维护模式
echo "Enabling maintenance mode before deployment..."
bash ./run-frappe-console.sh $tenant_id "bench --site all set-maintenance-mode on" "bench"
# 执行kubectl命令清理缓存
bash ./run-frappe-console.sh $tenant_id "frappe.client_cache.delete_value('assets_json', shared=False)" "console"

if [ "$deploy_type" = "migrate" ]; then
    # migrate
    echo "Migrating site..."
    bash ./run-frappe-console.sh $tenant_id "bench --site all migrate" "bench"

else
    echo "${config_json}" > ${tenant_id}_config.json 
    python3 helm-deploy.py --tenant-id ${tenant_id} --config ${tenant_id}_config.json --image-tag=$tag --$deploy_type
    # 等待30s，确保pod都完全起来
    sleep 60

fi
# 禁用维护模式
echo "Disabling maintenance mode after deployment..."
bash ./run-frappe-console.sh $tenant_id "bench --site all set-maintenance-mode off" "bench"


# 清楚站点缓存
echo "clear website cache after deployment..."
bash ./run-frappe-console.sh $tenant_id "bench --site all clear-website-cache" "bench"

# 清楚缓存
echo "clear cache after deployment..."
bash ./run-frappe-console.sh $tenant_id "bench --site all clear-cache" "bench"

# 发送更新通知
bash ./run-frappe-console.sh $tenant_id "frappe.publish_realtime('version-update')" "console"
# 提示完成
echo "******Job create done, please login [k8s dashboard] check job status*********"