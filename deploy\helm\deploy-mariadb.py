#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Frappe-Lemon 共享MariaDB部署脚本

此脚本部署一个独立的MariaDB服务，供所有Frappe租户共享使用。
"""

import os
import sys
import argparse
import yaml
import subprocess

# ANSI 颜色代码
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    CYAN = '\033[0;36m'
    GRAY = '\033[0;37m'
    NC = '\033[0m'  # 无颜色


def print_colored(message, color=""):
    """打印彩色文本"""
    color_code = getattr(Colors, color.upper(), "")
    print(f"{color_code}{message}{Colors.NC}")


def execute_command(command, check=True):
    """执行命令并返回结果"""
    try:
        print_colored(f"执行命令: {command}", "GRAY")
        result = subprocess.run(command, shell=True, check=check, text=True, capture_output=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print_colored(f"命令执行失败: {e}", "RED")
        if e.stderr:
            print(e.stderr)
        return False


class MariaDBDeployer:
    """
    MariaDB部署器类，负责部署一个独立的MariaDB服务供所有Frappe租户共享
    """
    
    def __init__(self):
        """初始化默认值和参数解析"""
        # 脚本位置和路径
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(self.script_dir, "output", "mariadb")
        
        # MariaDB配置
        self.db_root_password = "mariadb@123LEMON"
        self.storage_size = "50Gi"
        self.storage_class = "nfs-client"
        
        # 集群配置
        self.namespace = "frappe-lemon"
        self.release_name = "frappe-mariadb"
        
        # 解析命令行参数
        self.parse_arguments()
        
    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description="部署共享MariaDB服务")
        parser.add_argument("--root-password", help=f"MariaDB root密码（默认：{self.db_root_password}）")
        parser.add_argument("--storage-size", help=f"存储大小（默认：{self.storage_size}）")
        parser.add_argument("--storage-class", help=f"存储类名称（默认：{self.storage_class}）")
        parser.add_argument("--namespace", help=f"Kubernetes命名空间（默认：{self.namespace}）")
        parser.add_argument("--release-name", help=f"Helm发布名称（默认：{self.release_name}）")
        parser.add_argument("--output-dir", help="输出目录，用于存储生成的文件")
        
        args = parser.parse_args()
        
        # 处理可选项
        if args.root_password:
            self.db_root_password = args.root_password
        if args.storage_size:
            self.storage_size = args.storage_size
        if args.storage_class:
            self.storage_class = args.storage_class
        if args.namespace:
            self.namespace = args.namespace
        if args.release_name:
            self.release_name = args.release_name
        if args.output_dir:
            self.output_dir = args.output_dir
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_values_yaml(self):
        """生成Helm values.yaml文件"""
        # 构建values字典
        values = {
            "auth": {
                "rootPassword": self.db_root_password,
                "database": "mysql",  # 初始创建的系统数据库
                "username": "admin",  # 初始管理员用户
                "password": self.db_root_password  # 初始管理员密码
            },
            "primary": {
                "persistence": {
                    "enabled": True,
                    "storageClass": self.storage_class,
                    "size": self.storage_size
                },
                "resources": {
                    "requests": {
                        "memory": "1Gi",
                        "cpu": "500m"
                    },
                    "limits": {
                        "memory": "4Gi",
                        "cpu": "2"
                    }
                }
            },
            "service": {
                "type": "ClusterIP",
                "port": 3306
            },
            # 启用慢查询日志
            "configuration": """
[mysqld]
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
innodb_file_format = Barracuda
innodb_file_per_table = 1
innodb_large_prefix = 1
character-set-client-handshake = FALSE
max_allowed_packet = 512M
log_slow_queries = 1
long_query_time = 1
max_connections = 300
"""
        }
        
        # 写入values文件
        values_file = os.path.join(self.output_dir, "mariadb-values.yaml")
        with open(values_file, 'w') as f:
            yaml.dump(values, f, default_flow_style=False)
            
        print_colored(f"已生成Helm values文件: {values_file}", "GREEN")
        return values_file
    
    def create_namespace(self):
        """创建Kubernetes命名空间"""
        print_colored(f"确保命名空间 {self.namespace} 存在...", "CYAN")
        
        # 检查命名空间是否存在
        result = subprocess.run(["kubectl", "get", "namespace", self.namespace], capture_output=True, text=True)
        if result.returncode != 0:
            # 创建命名空间
            execute_command(f"kubectl create namespace {self.namespace}")
            print_colored(f"已创建命名空间: {self.namespace}", "GREEN")
        else:
            print_colored(f"命名空间 {self.namespace} 已存在", "CYAN")
    
    def deploy(self):
        """部署MariaDB"""
        print_colored("开始部署共享MariaDB服务...", "CYAN")
        
        # 创建命名空间
        self.create_namespace()
        
        # 生成values文件
        values_file = self.generate_values_yaml()
        
        # 添加Bitnami Helm仓库
        subprocess.run(["helm", "repo", "add", "bitnami", "https://charts.bitnami.com/bitnami"], capture_output=True)
        subprocess.run(["helm", "repo", "update"], capture_output=True)
        
        # 部署命令
        helm_cmd = f"helm upgrade --install {self.release_name} bitnami/mariadb --namespace {self.namespace} -f {values_file}"
        
        print_colored("执行Helm部署命令...", "CYAN")
        if execute_command(helm_cmd):
            print_colored(f"MariaDB已成功部署为: {self.release_name}", "GREEN")
            
            # 获取部署状态
            print_colored("\n部署状态:", "CYAN")
            execute_command(f"kubectl get pods -n {self.namespace} -l app.kubernetes.io/name=mariadb")
            
            # 输出连接信息
            print_colored("\n连接信息:", "GREEN")
            print_colored(f"MariaDB服务名: {self.release_name}.{self.namespace}.svc.cluster.local", "GREEN")
            print_colored(f"端口: 3306", "GREEN")
            print_colored(f"Root密码: {self.db_root_password}", "GREEN")
            print_colored(f"\n使用以下命令验证连接:", "YELLOW")
            print_colored(f"kubectl run mysql-client --rm --tty -i --restart='Never' --image docker.io/bitnami/mariadb:10.6.12 --namespace {self.namespace} -- mysql -h {self.release_name} -uroot -p{self.db_root_password}", "YELLOW")
        else:
            print_colored("部署失败，请检查错误信息。", "RED")
    
    def uninstall(self):
        """卸载MariaDB部署"""
        print_colored(f"卸载 {self.release_name} 部署...", "CYAN")
        
        # 卸载Helm release
        if execute_command(f"helm uninstall {self.release_name} --namespace {self.namespace}"):
            print_colored(f"{self.release_name} 已成功卸载", "GREEN")
        else:
            print_colored(f"卸载 {self.release_name} 失败", "RED")


if __name__ == "__main__":
    deployer = MariaDBDeployer()
    deployer.deploy()
