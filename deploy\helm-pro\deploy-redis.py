#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Frappe-Lemon 共享redis 部署脚本

此脚本部署一个独立的redis 服务，供所有Frappe租户共享使用。
"""

import os
import sys
import argparse
import yaml
import subprocess

# ANSI 颜色代码
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    CYAN = '\033[0;36m'
    GRAY = '\033[0;37m'
    NC = '\033[0m'  # 无颜色


def print_colored(message, color=""):
    """打印彩色文本"""
    color_code = getattr(Colors, color.upper(), "")
    print(f"{color_code}{message}{Colors.NC}")


def execute_command(command, check=True):
    """执行命令并返回结果"""
    try:
        print_colored(f"执行命令: {command}", "GRAY")
        result = subprocess.run(command, shell=True, check=check, text=True, capture_output=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print_colored(f"命令执行失败: {e}", "RED")
        if e.stderr:
            print(e.stderr)
        return False


class RedisDeployer:
    """
    redis 部署器类，负责部署一个独立的redis 服务供所有Frappe租户共享
    """
    
    def __init__(self):
        """初始化默认值和参数解析"""
        # 脚本位置和路径
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(self.script_dir, "output", "redis")
        
        # 集群配置
        self.namespace = "frappe-lemon"
        self.release_name = "frappe-redis"
        
        # 解析命令行参数
        self.parse_arguments()
        
    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description="部署共享redis 服务")
      
        parser.add_argument("--namespace", help=f"Kubernetes命名空间（默认：{self.namespace}）")
        parser.add_argument("--release-name", help=f"Helm发布名称（默认：{self.release_name}）")
        parser.add_argument("--output-dir", help="输出目录，用于存储生成的文件")
        
        
        
        args = parser.parse_args()
        
      
        if args.namespace:
            self.namespace = args.namespace
        if args.release_name:
            self.release_name = args.release_name
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_values_yaml(self):
        """生成Helm values.yaml文件"""
        # 构建values字典
        
        values = {
                "global": {
                    "imageRegistry": "docker.io"
                },
                "image": {
                    "registry": "docker.io",
                    "repository": "bitnami/redis",
                    "tag": "latest"
                   
                },
                "architecture": 'replication',
                "auth": {
                    "enabled": False,
                },
                "master": {
                    "persistence": {
                        "enabled": False
                    },
                    "resources": {
                        "limits": {
                            "memory": "4Gi"
                        }
                    }
                },
                "replica": {
                    "persistence": {
                        "enabled": False
                    }
                }
            }
        # 写入values文件
        values_file = os.path.join(self.output_dir, "redis-values.yaml")
        with open(values_file, 'w') as f:
            yaml.dump(values, f, default_flow_style=False)
            
        print_colored(f"已生成Helm values文件: {values_file}", "GREEN")
        return values_file
    
    def create_namespace(self):
        """创建Kubernetes命名空间"""
        print_colored(f"确保命名空间 {self.namespace} 存在...", "CYAN")
        
        # 检查命名空间是否存在
        result = subprocess.run(["kubectl", "get", "namespace", self.namespace], capture_output=True, text=True)
        if result.returncode != 0:
            # 创建命名空间
            execute_command(f"kubectl create namespace {self.namespace}")
            print_colored(f"已创建命名空间: {self.namespace}", "GREEN")
        else:
            print_colored(f"命名空间 {self.namespace} 已存在", "CYAN")
    
    def deploy(self):
        """部署redis """
        print_colored("开始部署共享redis 服务...", "CYAN")
        
        # 创建命名空间
        self.create_namespace()
        
        # 生成values文件
        values_file = self.generate_values_yaml()
        
        # 添加Bitnami Helm仓库
        subprocess.run(["helm", "repo", "add", "bitnami", "https://helm-charts.itboon.top/bitnami"], capture_output=True)
        subprocess.run(["helm", "repo", "update"], capture_output=True)
        
        # 部署命令
        helm_cmd = f"helm upgrade --install {self.release_name} bitnami/redis  --namespace {self.namespace} -f {values_file}"
        
        print_colored("执行Helm部署命令...", "CYAN")
        if execute_command(helm_cmd):
            print_colored(f"redis 已成功部署为: {self.release_name}", "GREEN")
            
            # 获取部署状态
            print_colored("\n部署状态:", "CYAN")
            execute_command(f"kubectl get pods -n {self.namespace} -l app.kubernetes.io/name=redis")
            
            # 输出连接信息
            print_colored("\n连接信息:", "GREEN")
            print_colored(f"redis 服务名: {self.release_name}.{self.namespace}.svc.cluster.local", "GREEN")
           
        else:
            print_colored("部署失败，请检查错误信息。", "RED")
    
    def uninstall(self):
        """卸载redis 部署"""
        print_colored(f"卸载 {self.release_name} 部署...", "CYAN")
        
        # 卸载Helm release
        if execute_command(f"helm uninstall {self.release_name} --namespace {self.namespace}"):
            print_colored(f"{self.release_name} 已成功卸载", "GREEN")
        else:
            print_colored(f"卸载 {self.release_name} 失败", "RED")


if __name__ == "__main__":
    deployer = RedisDeployer()
    deployer.deploy()
