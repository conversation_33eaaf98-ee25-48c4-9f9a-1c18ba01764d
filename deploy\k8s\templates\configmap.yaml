apiVersion: v1
kind: ConfigMap
metadata:
  name: ${TENANT_ID}-config
  namespace: frappe-lemon
  labels:
    tenant: ${TENANT_ID}
data:
  FRAPPE_SITE_NAME: "${TENANT_ID}.lemonstudio.tech"
  MARIADB_HOST: "localhost"
  REDIS_CACHE: "localhost:6379"
  REDIS_QUEUE: "localhost:6379"
  REDIS_SOCKETIO: "localhost:6379"
  SITE_NAME: "${SUBDOMAIN}.${DOMAIN_SUFFIX}"
  LEMON_TENANT_ID: "${TENANT_ID}"
  
  # 站点初始化配置
  ADMIN_PASSWORD: "${ADMIN_PASSWORD:-admin}"
  DB_NAME: "${DB_NAME:-frappe}"
  DB_PASSWORD: "${DB_PASSWORD:-frappe}"
  DB_ROOT_PASSWORD: "${DB_ROOT_PASSWORD:-mariadb}"
  INSTALL_APPS: "${INSTALL_APPS:-erpnext,lemon_factory}"
  INITIALIZE_SITE: "${INITIALIZE_SITE:-true}"
