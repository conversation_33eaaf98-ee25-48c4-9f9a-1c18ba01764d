apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${TENANT_ID}-ingress
  namespace: frappe-lemon
  labels:
    tenant: ${TENANT_ID}
  annotations:
    kubernetes.io/ingress.class: "${INGRESS_CLASS:-nginx}"
    nginx.ingress.kubernetes.io/proxy-body-size: "${MAX_BODY_SIZE:-50m}"
    # Add more annotations as needed
spec:
  rules:
  - host: "${SUBDOMAIN}.${DOMAIN_SUFFIX:-lemonstudio.tech}"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ${TENANT_ID}-nginx
            port:
              number: 80
      - path: /socket.io
        pathType: Prefix
        backend:
          service:
            name: ${TENANT_ID}-websocket
            port:
              number: 9000
  # Configure TLS as needed
  # tls:
  # - hosts:
  #   - "${SUBDOMAIN}.${DOMAIN_SUFFIX:-lemonstudio.tech}"
  #   secretName: ${TENANT_ID}-tls-secret
