#!/bin/bash

# 脚本有任意错误直接退出
set -e

# 参数
# $1: tenant_id - 租户ID
# $2: command - 要执行的命令
# $3: command_type - 命令类型，可选值: console(默认), bench
# $4: wait_time - 重试等待时间，默认10秒

tenant_id=$1
command=$2
command_type=${3:-"console"}
wait_time=${4:-10}
max_retries=3
retry_count=0

function find_pod() {
  # 获取第一个匹配的 Pod 名称，且确保该Pod处于Running状态
  kubectl get pods -n frappe-lemon \
    -l app.kubernetes.io/instance=frappe-${tenant_id}-gunicorn \
    -l app.kubernetes.io/name=erpnext-gunicorn \
    --field-selector=status.phase=Running \
    -o jsonpath='{.items[0].metadata.name}'
}

# 判断是否是bench命令类型
is_bench_command() {
  [[ "$command_type" == "bench" ]]
}

# 执行命令的函数
execute_command() {
  local pod_name=$1
  
  if is_bench_command; then
    # 如果是bench命令类型，直接执行
    echo "Executing as bench command: $command"
    kubectl exec -it $pod_name -n frappe-lemon -- $command
  else
    # 如果是console命令类型，通过console执行
    echo "Executing as console command"
    kubectl exec -it $pod_name -n frappe-lemon -- bench --site all console <<EOF
$command
EOF
  fi
}

# 尝试查找并执行命令，如果找不到pod则重试
while [ $retry_count -lt $max_retries ]; do
  POD_NAME=$(find_pod)
  
  if [ -n "$POD_NAME" ]; then
    echo "Found pod: $POD_NAME"
    execute_command "$POD_NAME"
    exit 0
  else
    retry_count=$((retry_count + 1))
    
    if [ $retry_count -lt $max_retries ]; then
      echo "No matching Pod found! Waiting for ${wait_time} seconds before retry (${retry_count}/${max_retries})..."
      sleep $wait_time
    else
      echo "No matching Pod found after ${max_retries} attempts. Giving up."
      exit 1
    fi
  fi
done