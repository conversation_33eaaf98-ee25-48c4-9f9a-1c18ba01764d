apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${TENANT_ID}-frappe
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-frappe
    tenant: ${TENANT_ID}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ${TENANT_ID}-frappe
      tenant: ${TENANT_ID}
  template:
    metadata:
      labels:
        app: ${TENANT_ID}-frappe
        tenant: ${TENANT_ID}
    spec:
      imagePullSecrets:
      - name: harborsecret
      containers:
      # MariaDB 容器
      - name: mariadb
        image: mariadb:10.6
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "${DB_ROOT_PASSWORD:-mariadb}"
        - name: MYSQL_DATABASE
          value: "${DB_NAME:-frappe}"
        ports:
        - containerPort: 3306
          name: mariadb
        volumeMounts:
        - name: tenant-data
          mountPath: /var/lib/mysql
          subPath: mariadb
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      
      # Redis 容器 - Cache
      - name: redis-cache
        image: redis:6.2-alpine
        command: ["redis-server", "--save", "", "--maxmemory-policy", "allkeys-lru"]
        ports:
        - containerPort: 6379
          name: cache
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

      # Redis 容器 - Queue
      - name: redis-queue
        image: redis:6.2-alpine
        command: ["redis-server", "--save", "", "--maxmemory-policy", "allkeys-lru"]
        ports:
        - containerPort: 6379
          name: queue
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

      # Redis 容器 - SocketIO
      - name: redis-socketio
        image: redis:6.2-alpine
        command: ["redis-server", "--save", "", "--maxmemory-policy", "allkeys-lru"]
        ports:
        - containerPort: 6379
          name: socketio
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
            
      # 后端容器
      - name: backend
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["backend-entrypoint.sh"]
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        resources:
          requests:
            memory: "${BACKEND_MEMORY_REQUEST:-512Mi}"
            cpu: "${BACKEND_CPU_REQUEST:-500m}"
          limits:
            memory: "${BACKEND_MEMORY_LIMIT:-1Gi}"
            cpu: "${BACKEND_CPU_LIMIT:-1000m}"
        ports:
        - containerPort: 8000
          name: backend
        volumeMounts:
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/sites
          subPath: sites
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/logs
          subPath: logs
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/sites/assets
          subPath: assets
        
      # Nginx 容器
      - name: nginx
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["nginx-entrypoint.sh"]
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        resources:
          requests:
            memory: "${NGINX_MEMORY_REQUEST:-128Mi}"
            cpu: "${NGINX_CPU_REQUEST:-100m}"
          limits:
            memory: "${NGINX_MEMORY_LIMIT:-256Mi}"
            cpu: "${NGINX_CPU_LIMIT:-200m}"
        ports:
        - containerPort: 8080
          name: http
        volumeMounts:
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/sites
          subPath: sites
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/logs
          subPath: logs
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/sites/assets
          subPath: assets
      
      # WebSocket 容器
      - name: websocket
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["websocket-entrypoint.sh"]
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        resources:
          requests:
            memory: "${WEBSOCKET_MEMORY_REQUEST:-256Mi}"
            cpu: "${WEBSOCKET_CPU_REQUEST:-100m}"
          limits:
            memory: "${WEBSOCKET_MEMORY_LIMIT:-512Mi}"
            cpu: "${WEBSOCKET_CPU_LIMIT:-200m}"
        ports:
        - containerPort: 9000
          name: websocket
        volumeMounts:
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/sites
          subPath: sites
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/logs
          subPath: logs
      
      volumes:
      - name: tenant-data
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-data-pvc
