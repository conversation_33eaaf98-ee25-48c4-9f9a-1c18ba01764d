# 数据存储卷

apiVersion: v1
kind: PersistentVolume
metadata:
  name: ${TENANT_ID}-data-pv
  labels:
    tenant: ${TENANT_ID}
spec:
  capacity:
    storage: ${TOTAL_STORAGE_SIZE:-20Gi}
  accessModes:
  - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  nfs:
    server: ${NFS_SERVER}
    path: ${NFS_BASE_PATH}/frappe-lemon/${TENANT_ID}
    
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: ${TENANT_ID}-data-pvc
  namespace: frappe-lemon
  labels:
    tenant: ${TENANT_ID}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: ${TOTAL_STORAGE_SIZE:-20Gi}
  volumeName: ${TENANT_ID}-data-pv
