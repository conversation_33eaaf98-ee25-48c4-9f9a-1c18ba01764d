#!/bin/bash
# Docker image build script for Frappe-Lemon

# Default parameter values
IMAGE_TYPE="all"           # Options: base, build, builder, factory, runtime, all
DOCKER_REGISTRY="harbor.lemonstudio.tech"
DOCKER_NAMESPACE="lemon-factory"
TAG="latest"
FRAPPE_BRANCH="develop"
FRAPPE_TAG=""
FRAPPE_COMMIT=""
FRAPPE_REPO="https://github.com/frappe/frappe"
ERPNEXT_BRANCH="develop"
ERPNEXT_TAG=""
ERPNEXT_COMMIT=""
ERPNEXT_REPO="https://github.com/frappe/erpnext"
HRMS_BRANCH="develop"
HRMS_TAG=""
HRMS_COMMIT=""
HRMS_REPO="https://github.com/frappe/hrms"
PRINTDESIGN_BRANCH="develop"
PRINTDESIGN_TAG=""
PRINTDESIGN_COMMIT=""
PRINTDESIGN_REPO="ssh://<EMAIL>:29418/print-designer"
LEMON_FACTORY_BRANCH="develop"
LEMON_FACTORY_REPO="ssh://<EMAIL>:29418/lemon-factory"
NO_BUILD_CACHE=false
PUSH_IMAGES=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --image-type=*)
      IMAGE_TYPE="${1#*=}"
      shift
      ;;
    --docker-registry=*)
      DOCKER_REGISTRY="${1#*=}"
      shift
      ;;
    --docker-namespace=*)
      DOCKER_NAMESPACE="${1#*=}"
      shift
      ;;
    --tag=*)
      TAG="${1#*=}"
      shift
      ;;
    --frappe-branch=*)
      FRAPPE_BRANCH="${1#*=}"
      shift
      ;;
    --frappe-tag=*)
      FRAPPE_TAG="${1#*=}"
      FRAPPE_COMMIT="" # Clear commit if tag is specified
      shift
      ;;
    --frappe-commit=*)
      FRAPPE_COMMIT="${1#*=}"
      FRAPPE_TAG="" # Clear tag if commit is specified
      shift
      ;;
    --frappe-repo=*)
      FRAPPE_REPO="${1#*=}"
      shift
      ;;
    --erpnext-branch=*)
      ERPNEXT_BRANCH="${1#*=}"
      shift
      ;;
    --erpnext-tag=*)
      ERPNEXT_TAG="${1#*=}"
      ERPNEXT_COMMIT="" # Clear commit if tag is specified
      shift
      ;;
    --erpnext-commit=*)
      ERPNEXT_COMMIT="${1#*=}"
      ERPNEXT_TAG="" # Clear tag if commit is specified
      shift
      ;;
    --erpnext-repo=*)
      ERPNEXT_REPO="${1#*=}"
      shift
      ;;
    --hrms-branch=*)
      HRMS_BRANCH="${1#*=}"
      shift
      ;;
    --hrms-tag=*)
      HRMS_TAG="${1#*=}"
      HRMS_COMMIT="" # Clear commit if tag is specified
      shift
      ;;
    --hrms-commit=*)
      HRMS_COMMIT="${1#*=}"
      HRMS_TAG="" # Clear tag if commit is specified
      shift
      ;;
    --hrms-repo=*)
      HRMS_REPO="${1#*=}"
      shift
      ;;
    --printdesign-branch=*)
      PRINTDESIGN_BRANCH="${1#*=}"
      shift
      ;;
    --printdesign-tag=*)
      PRINTDESIGN_TAG="${1#*=}"
      PRINTDESIGN_COMMIT="" # Clear commit if tag is specified
      shift
      ;;
    --printdesign-commit=*)
      PRINTDESIGN_COMMIT="${1#*=}"
      PRINTDESIGN_TAG="" # Clear tag if commit is specified
      shift
      ;;
    --printdesign-repo=*)
      PRINTDESIGN_REPO="${1#*=}"
      shift
      ;;
    --lemon-factory-branch=*)
      LEMON_FACTORY_BRANCH="${1#*=}"
      shift
      ;;
    --lemon-factory-repo=*)
      LEMON_FACTORY_REPO="${1#*=}"
      shift
      ;;
    --no-build-cache)
      NO_BUILD_CACHE=true
      shift
      ;;
    --push-images)
      PUSH_IMAGES=true
      shift
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --image-type=TYPE         Image type to build (base, build, builder, factory, runtime, all)"
      echo "  --docker-registry=URL     Docker registry URL"
      echo "  --docker-namespace=NAME   Docker namespace"
      echo "  --tag=TAG                 Image tag"
      echo "  --frappe-branch=BRANCH    Frappe branch"
      echo "  --frappe-tag=TAG          Frappe git tag (overrides branch if specified)"
      echo "  --frappe-commit=HASH      Frappe git commit hash (overrides branch and tag if specified)"
      echo "  --frappe-repo=URL         Frappe repository URL"
      echo "  --erpnext-branch=BRANCH   ERPNext branch"
      echo "  --erpnext-tag=TAG         ERPNext git tag (overrides branch if specified)"
      echo "  --erpnext-commit=HASH     ERPNext git commit hash (overrides branch and tag if specified)"
      echo "  --erpnext-repo=URL        ERPNext repository URL"
      echo "  --hrms-branch=BRANCH      HRMS branch"
      echo "  --hrms-tag=TAG            HRMS git tag (overrides branch if specified)"
      echo "  --hrms-commit=HASH        HRMS git commit hash (overrides branch and tag if specified)"
      echo "  --hrms-repo=URL           HRMS repository URL"
      echo "  --printdesign-branch=BRANCH   PRINTDESIGN branch"
      echo "  --printdesign-tag=TAG         PRINTDESIGN git tag (overrides branch if specified)"
      echo "  --printdesign-commit=HASH     PRINTDESIGN git commit hash (overrides branch and tag if specified)"
      echo "  --printdesign-repo=URL        PRINTDESIGN repository URL"
      echo "  --lemon-factory-branch=BRANCH  Lemon Factory branch"
      echo "  --lemon-factory-repo=URL  Lemon Factory repository URL"
      echo "  --no-build-cache          Build without cache"
      echo "  --push-images             Push images after building"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# ANSI color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Function to print colored messages
print_colored_output() {
  local message="$1"
  local color="$2"

  case "$color" in
    "Red") echo -e "${RED}${message}${NC}" ;;
    "Green") echo -e "${GREEN}${message}${NC}" ;;
    "Yellow") echo -e "${YELLOW}${message}${NC}" ;;
    "Cyan") echo -e "${CYAN}${message}${NC}" ;;
    "Gray") echo -e "${GRAY}${message}${NC}" ;;
    *) echo "$message" ;;
  esac
}

# Function to build a Docker image with error handling
build_docker_image() {
  local dockerfile="$1"
  local image_name="$2"
  local build_args="$3"
  local push="$4"

  print_colored_output "Building image: $image_name from $dockerfile" "Cyan"

  local no_cache_arg=""
  if [ "$NO_BUILD_CACHE" = true ]; then
    no_cache_arg="--no-cache"
  fi

  local build_command="docker build --add-host=gerrit.lemonstudio.tech:************** $no_cache_arg $build_args -t $DOCKER_REGISTRY/$DOCKER_NAMESPACE/$image_name -f $dockerfile ."
  print_colored_output "Running command: $build_command" "Gray"

  eval $build_command
  build_exit_code=$?

  if [ $build_exit_code -ne 0 ]; then
    print_colored_output "Failed to build image $image_name" "Red"
    return 1
  fi

  print_colored_output "Successfully built image $image_name" "Green"

  if [ "$push" = true ]; then
    print_colored_output "Pushing image: $DOCKER_REGISTRY/$DOCKER_NAMESPACE/$image_name" "Yellow"
    docker push "$DOCKER_REGISTRY/$DOCKER_NAMESPACE/$image_name"
    push_exit_code=$?

    if [ $push_exit_code -ne 0 ]; then
      print_colored_output "Failed to push image $image_name" "Red"
      return 1
    fi

    print_colored_output "Successfully pushed image $image_name" "Green"
  fi

  return 0
}

# Validate image type input
valid_image_types=("base" "build" "builder" "factory" "runtime" "all")
valid_image_type=false

for type in "${valid_image_types[@]}"; do
  if [ "$type" = "$IMAGE_TYPE" ]; then
    valid_image_type=true
    break
  fi
done

if [ "$valid_image_type" = false ]; then
  print_colored_output "Invalid image type: $IMAGE_TYPE. Valid options are: ${valid_image_types[*]}" "Red"
  exit 1
fi

# Build the requested images
build_success=true

# Common build args for all images
common_build_args="--build-arg DOCKER_REGISTRY=$DOCKER_REGISTRY --build-arg DOCKER_NAMESPACE=$DOCKER_NAMESPACE"

# Specific image builds
if [ "$IMAGE_TYPE" = "base" ] || [ "$IMAGE_TYPE" = "all" ]; then
  build_docker_image "Dockerfile.base" "frappe-base:$TAG" "" "$PUSH_IMAGES"
  if [ $? -ne 0 ]; then
    build_success=false
  fi
fi

if ([ "$IMAGE_TYPE" = "build" ] || [ "$IMAGE_TYPE" = "all" ]) && [ "$build_success" = true ]; then
  build_args="$common_build_args"
  build_docker_image "Dockerfile.build" "frappe-build:$TAG" "$build_args" "$PUSH_IMAGES"
  if [ $? -ne 0 ]; then
    build_success=false
  fi
fi

if ([ "$IMAGE_TYPE" = "builder" ] || [ "$IMAGE_TYPE" = "all" ]) && [ "$build_success" = true ]; then
  build_args="$common_build_args --build-arg FRAPPE_REPO=$FRAPPE_REPO --build-arg ERPNEXT_REPO=$ERPNEXT_REPO --build-arg HRMS_REPO=$HRMS_REPO --build-arg PRINTDESIGN_REPO=$PRINTDESIGN_REPO"

  # Add Frappe version arguments (commit > tag > branch) based on what's specified
  if [ -n "$FRAPPE_COMMIT" ]; then
    build_args="$build_args --build-arg FRAPPE_COMMIT=$FRAPPE_COMMIT"
  elif [ -n "$FRAPPE_TAG" ]; then
    build_args="$build_args --build-arg FRAPPE_TAG=$FRAPPE_TAG"
  else
    build_args="$build_args --build-arg FRAPPE_BRANCH=$FRAPPE_BRANCH"
  fi

  # Add ERPNext version arguments (commit > tag > branch) based on what's specified
  if [ -n "$ERPNEXT_COMMIT" ]; then
    build_args="$build_args --build-arg ERPNEXT_COMMIT=$ERPNEXT_COMMIT"
  elif [ -n "$ERPNEXT_TAG" ]; then
    build_args="$build_args --build-arg ERPNEXT_TAG=$ERPNEXT_TAG"
  else
    build_args="$build_args --build-arg ERPNEXT_BRANCH=$ERPNEXT_BRANCH"
  fi

  # Add HRMS version arguments (commit > tag > branch) based on what's specified
  if [ -n "$HRMS_COMMIT" ]; then
    build_args="$build_args --build-arg HRMS_COMMIT=$HRMS_COMMIT"
  elif [ -n "$HRMS_TAG" ]; then
    build_args="$build_args --build-arg HRMS_TAG=$HRMS_TAG"
  else
    build_args="$build_args --build-arg HRMS_BRANCH=$HRMS_BRANCH"
  fi

  # Add PrintDesign version arguments (commit > tag > branch) based on what's specified
  if [ -n "$PRINTDESIGN_COMMIT" ]; then
    build_args="$build_args --build-arg PRINTDESIGN_COMMIT=$PRINTDESIGN_COMMIT"
  elif [ -n "$PRINTDESIGN_TAG" ]; then
    build_args="$build_args --build-arg PRINTDESIGN_TAG=$PRINTDESIGN_TAG"
  else
    build_args="$build_args --build-arg PRINTDESIGN_BRANCH=$PRINTDESIGN_BRANCH"
  fi
  build_docker_image "Dockerfile.builder" "frappe-builder:$TAG" "$build_args" "$PUSH_IMAGES"
  if [ $? -ne 0 ]; then
    build_success=false
  fi
fi

if ([ "$IMAGE_TYPE" = "factory" ] || [ "$IMAGE_TYPE" = "all" ]) && [ "$build_success" = true ]; then
  build_args="$common_build_args --build-arg LEMON_FACTORY_BRANCH=$LEMON_FACTORY_BRANCH --build-arg LEMON_FACTORY_REPO=$LEMON_FACTORY_REPO  --build-arg PRINTDESIGN_REPO=$PRINTDESIGN_REPO --build-arg PRINTDESIGN_BRANCH=$PRINTDESIGN_BRANCH"
  build_docker_image "Dockerfile.factory" "frappe-factory:$TAG" "$build_args" "$PUSH_IMAGES"
  if [ $? -ne 0 ]; then
    build_success=false
  fi
fi

if ([ "$IMAGE_TYPE" = "runtime" ] || [ "$IMAGE_TYPE" = "all" ]) && [ "$build_success" = true ]; then
  build_args="$common_build_args --build-arg TAG=$TAG"
  build_docker_image "Dockerfile" "frappe:$TAG" "$build_args" "$PUSH_IMAGES"
  if [ $? -ne 0 ]; then
    build_success=false
  fi
fi

# Final status
if [ "$build_success" = true ]; then
  print_colored_output "All requested images built successfully" "Green"
else
  print_colored_output "Some image builds failed" "Red"
  exit 1
fi
