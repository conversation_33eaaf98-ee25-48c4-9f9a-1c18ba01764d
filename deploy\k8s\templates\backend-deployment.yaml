apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${TENANT_ID}-backend
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-backend
    tenant: ${TENANT_ID}
spec:
  replicas: ${BACKEND_REPLICAS:-1}
  selector:
    matchLabels:
      app: ${TENANT_ID}-backend
  template:
    metadata:
      labels:
        app: ${TENANT_ID}-backend
    spec:
      containers:
      - name: backend
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["backend-entrypoint.sh"]
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        resources:
          requests:
            memory: "${BACKEND_MEMORY_REQUEST:-512Mi}"
            cpu: "${BACKEND_CPU_REQUEST:-200m}"
          limits:
            memory: "${BACKEND_MEMORY_LIMIT:-1Gi}"
            cpu: "${BACKEND_CPU_LIMIT:-500m}"
        volumeMounts:
        - name: sites-dir
          mountPath: /home/<USER>/lemon-bench/sites
        - name: logs-dir
          mountPath: /home/<USER>/lemon-bench/logs
      volumes:
      - name: sites-dir
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-sites-pvc
      - name: logs-dir
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-logs-pvc
