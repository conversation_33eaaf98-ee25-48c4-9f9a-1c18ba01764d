apiVersion: batch/v1
kind: Job
metadata:
  name: ${TENANT_ID}-site-init
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-site-init
    tenant: ${TENANT_ID}
spec:
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: ${TENANT_ID}-site-init
        tenant: ${TENANT_ID}
    spec:
      restartPolicy: Never
      imagePullSecrets:
      - name: harborsecret
      containers:
      - name: site-init
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["/bin/bash", "-c"]
        args:
          - |
            set -e
            echo "开始初始化站点: ${SITE_NAME}..."
            cd /home/<USER>/lemon-bench
            
            # 等待数据库准备就绪
            echo "等待数据库准备就绪..."
            # 连接到主服务中的mariadb端口
            until mysql -h${TENANT_ID}-frappe.frappe-lemon.svc.cluster.local -P3306 -uroot -p${DB_ROOT_PASSWORD} -e "SELECT 1"; do
              echo "等待数据库连接..."
              sleep 2
            done
            
            # 检查站点是否已经存在
            if [ -d "sites/${SITE_NAME}" ]; then
              echo "站点已存在，跳过初始化..."
              exit 0
            fi
            
            # 创建新站点
            echo "创建新站点: ${SITE_NAME}..."
            bench new-site ${SITE_NAME} \
              --mariadb-root-password ${DB_ROOT_PASSWORD} \
              --admin-password ${ADMIN_PASSWORD} \
              --db-name ${DB_NAME} \
              --db-password ${DB_PASSWORD} \
              --db-type mariadb \
              --db-host ${MARIADB_HOST} \
              --mariadb-user-host-login-scope=%
            
            # 安装应用程序
            if [ ! -z "${INSTALL_APPS}" ]; then
              echo "安装应用程序: ${INSTALL_APPS}..."
              IFS=',' read -ra APPS <<< "${INSTALL_APPS}"
              for app in "${APPS[@]}"; do
                echo "安装应用: $app"
                bench --site ${SITE_NAME} install-app $app
              done
            fi
            
            # 设置为当前站点
            echo "设置 ${SITE_NAME} 为当前站点..."
            bench use ${SITE_NAME}
            
            # 其他配置设置
            echo "设置站点配置..."
            bench --site ${SITE_NAME} set-config maintenance_mode 0
            bench --site ${SITE_NAME} clear-cache
            
            echo "站点 ${SITE_NAME} 初始化完成!"
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        volumeMounts:
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/sites
          subPath: sites
        - name: tenant-data
          mountPath: /home/<USER>/lemon-bench/logs
          subPath: logs
      volumes:
      - name: tenant-data
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-data-pvc
