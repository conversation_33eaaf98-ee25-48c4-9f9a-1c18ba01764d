ingress:
  enabled: true
  ingressName: "site1-ingress" # 路由名称，环境变量INGRESS_NAME
  annotations:
    # 使用 Let's Encrypt 生产环境签发证书
    #cert-manager.io/cluster-issuer: letsencrypt-prod
    # 指定 Ingress 控制器类型
    kubernetes.io/ingress.class: nginx
    # 启用 ACME 协议自动获取证书
    kubernetes.io/tls-acme: "true"
    # 强制 HTTPS 重定向
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    # 允许大文件上传
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
  hosts:
  - host: site1.example.com # 域名，环境变量SITE
    paths:
    - path: /
      pathType: ImplementationSpecific
  tls:
  - secretName: site1-ssl # SSL保密字典，环境变量INGRESS_TLS_SECRET_NAME
    hosts:
      - site1.example.com # 域名，环境变量SITE