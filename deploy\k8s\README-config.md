# Frappe-Lemon 多租户部署配置指南

本文档介绍了如何使用配置文件和命令行参数配置Frappe-Lemon多租户部署。

## 配置文件支持

`deploy-tenant.py` 脚本现在支持通过JSON配置文件进行配置，这使得管理多个租户部署变得更加容易。

### 基本用法

使用配置文件运行部署脚本：

```bash
python deploy-tenant.py --config tenant-config.json
```

配置文件可以与命令行参数结合使用，命令行参数将覆盖配置文件中的设置：

```bash
python deploy-tenant.py --config tenant-config.json --subdomain custom-domain
```

### 配置文件格式

配置文件使用JSON格式，包含以下可配置项：

```json
{
  "tenant_id": "tenant1",
  "subdomain": "custom-domain",
  "docker_registry": "harbor.lemonstudio.tech",
  "docker_namespace": "lemon-factory",
  "image_tag": "latest",
  "domain_suffix": "lemonstudio.tech",
  "storage_class_name": "standard",
  "backend_replicas": 1,
  "nginx_replicas": 1,
  "websocket_replicas": 1,
  "sites_storage_size": "5Gi",
  "assets_storage_size": "5Gi",
  "logs_storage_size": "2Gi",
  "ingress_class": "nginx",
  "max_body_size": "50m",
  "alicloud_dns": {
    "enable": true,
    "access_key_id": "YOUR_ACCESS_KEY_ID",
    "access_key_secret": "YOUR_ACCESS_KEY_SECRET",
    "region": "cn-hangzhou",
    "ingress_ip": "*******"
  }
}
```

### 配置项说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `tenant_id` | 租户ID（必填） | - |
| `subdomain` | 自定义二级域名 | 同租户ID |
| `docker_registry` | Docker 仓库地址 | harbor.lemonstudio.tech |
| `docker_namespace` | Docker 命名空间 | lemon-factory |
| `image_tag` | 镜像标签 | latest |
| `domain_suffix` | 域名后缀 | lemonstudio.tech |
| `storage_class_name` | 存储类名称 | standard |
| `backend_replicas` | 后端副本数 | 1 |
| `nginx_replicas` | Nginx 副本数 | 1 |
| `websocket_replicas` | WebSocket 副本数 | 1 |
| `sites_storage_size` | 站点存储大小 | 5Gi |
| `assets_storage_size` | 资源存储大小 | 5Gi |
| `logs_storage_size` | 日志存储大小 | 2Gi |
| `ingress_class` | Ingress类 | nginx |
| `max_body_size` | 最大请求体大小 | 50m |

### 阿里云DNS配置

阿里云DNS配置项在 `alicloud_dns` 对象中：

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `enable` | 是否启用阿里云DNS | false |
| `access_key_id` | 阿里云 AccessKey ID | - |
| `access_key_secret` | 阿里云 AccessKey Secret | - |
| `region` | 阿里云区域 | cn-hangzhou |
| `ingress_ip` | Ingress控制器的外部IP地址 | - |

## 命令行参数

所有配置项也可以通过命令行参数设置。命令行参数将覆盖配置文件中的设置。

常用命令行参数：

```bash
# 基本用法
python deploy-tenant.py --tenant-id tenant1

# 使用配置文件
python deploy-tenant.py --config tenant-config.json

# 配置文件与命令行参数结合
python deploy-tenant.py --config tenant-config.json --subdomain custom-domain

# 启用阿里云DNS
python deploy-tenant.py --tenant-id tenant1 --aliyun-dns --access-key-id YOUR_KEY --access-key-secret YOUR_SECRET --ingress-ip *******
```

## 使用示例

### 批量部署多个租户

创建多个租户配置文件：

```
tenant1.json
tenant2.json
tenant3.json
```

然后使用脚本循环部署：

```bash
for config in tenant*.json; do
  python deploy-tenant.py --config $config
done
```

### 使用相同配置模板部署多个租户

创建一个配置模板 `tenant-template.json`，然后在部署时仅指定不同的租户ID：

```bash
python deploy-tenant.py --config tenant-template.json --tenant-id tenant1
python deploy-tenant.py --config tenant-template.json --tenant-id tenant2
```

## 注意事项

1. 租户ID是必需的参数，必须通过配置文件或命令行参数提供
2. 如果同时在配置文件和命令行中指定了同一个参数，命令行参数将优先使用
3. 启用阿里云DNS时，必须提供AccessKey ID、AccessKey Secret和Ingress IP地址
