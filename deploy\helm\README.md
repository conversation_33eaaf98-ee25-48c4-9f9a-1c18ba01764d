# Frappe Lemon Helm部署工具

此目录包含使用官方Frappe/ERPNext Helm chart部署多租户Frappe应用的统一部署工具。

## 概述

`helm-deploy.py` 脚本使用官方的Frappe/ERPNext Helm chart来部署Frappe Lemon应用，支持多租户架构、NFS存储和自定义Docker镜像。

## 新特性（v2.0）

- **统一部署脚本**：合并了原来的 helm 和 helm-pro 两套脚本，现在只需要一个脚本即可支持不同环境
- **环境支持**：支持 `dev` 和 `pro` 两种环境配置，自动设置不同的默认值
- **TLS 证书管理**：支持 certbot 或不使用 TLS
- **Redis 配置**：pro 环境支持外部 Redis 配置
- **HRMS 应用**：默认包含 HRMS 人力资源管理应用，安装顺序：erpnext → hrms → lemon_factory

## 先决条件

- Kubernetes集群
- Helm 3+
- kubectl已配置
- NFS服务器（用于持久化存储）
- 私有Docker仓库（可选）

## 使用方法

### 基本用法

```bash
# 开发环境部署（默认）
python helm-deploy.py --tenant-id your-tenant-id

# 生产环境部署
python helm-deploy.py --tenant-id your-tenant-id --environment pro
```

### 使用配置文件

```bash
# 开发环境
python helm-deploy.py --tenant-id your-tenant-id --config path/to/config.json

# 生产环境
python helm-deploy.py --tenant-id your-tenant-id --config path/to/config.json --environment pro
```

### TLS 证书配置

```bash
# 使用 certbot（生产环境默认）
python helm-deploy.py --tenant-id your-tenant-id --use-certbot --certbot-email <EMAIL>

# 不使用 TLS（仅 HTTP）
python helm-deploy.py --tenant-id your-tenant-id --no-tls
```

### 完整参数列表

```
基本参数：
--tenant-id          租户ID（必填）
--subdomain          子域名（默认与租户ID相同）
--config             配置文件路径
--environment        部署环境（dev 或 pro，默认：dev）

Docker配置：
--docker-registry    Docker仓库地址
--docker-namespace   Docker命名空间
--image-tag          镜像标签
--docker-config      Docker配置文件路径

网络配置：
--domain-suffix      域名后缀

存储配置：
--nfs-server         NFS服务器地址
--nfs-base-path      NFS基础路径
--storage-size       存储大小

站点配置：
--admin-password     管理员密码
--db-name            数据库名称
--db-password        数据库密码
--db-root-password   数据库root密码
--install-apps       安装应用列表，逗号分隔

TLS配置：
--use-certbot        使用主机上的certbot管理TLS证书
--no-tls             不配置TLS证书，仅使用HTTP
--certbot-email      用于Let's Encrypt注册的邮箱地址

其他：
--output-dir         输出目录
```

## 配置文件示例

参见 `default_config.example.json`：

```json
{
  "tenant_id": "demo-tenant",
  "subdomain": "demo-tenant",
  "docker_registry": "harbor.lemonstudio.tech",
  "docker_namespace": "lemon-factory",
  "image_tag": "latest",
  "domain_suffix": "lemonstudio.tech",
  "nfs_server": "172.24.187.107",
  "nfs_base_path": "/data0/nfs/frappe-lemon",
  "storage_size": "20Gi",
  "backend_replicas": 1,
  "nginx_replicas": 1,
  "websocket_replicas": 1,
  "site_config": {
    "admin_password": "admin@321LEMON",
    "db_name": "frappe",
    "db_password": "dbadmin@123LEMON",
    "db_root_password": "mariadb@123LEMON",
    "install_apps": "erpnext,hrms,lemon_factory"
  }
}
```

## 环境配置

### 开发环境（dev）
- NFS 服务器：172.24.187.107
- NFS 路径：/data0/nfs/frappe-lemon
- 镜像标签：latest
- TLS：默认不启用
- Redis：使用内置 Redis

### 生产环境（pro）
- NFS 服务器：172.24.187.99
- NFS 路径：/data1/nfs/frappe-lemon
- 镜像标签：pro
- TLS：默认使用 certbot
- Redis：支持外部 Redis 配置

## 与现有Docker镜像的兼容性

此部署工具完全兼容现有的Docker分层架构（base → build → builder → factory → runtime），并使用harbor.lemonstudio.tech中的lemon-factory命名空间下的私有镜像。

## 部署流程

1. 添加Frappe Helm仓库
2. 生成values.yaml文件
3. 创建Kubernetes命名空间（如果不存在）
4. 使用Helm部署Frappe/ERPNext
5. 显示部署状态和访问信息

## 与原有部署脚本的区别

相比原有的`deploy-tenant.py`脚本，此Helm部署工具有以下优势：

1. 使用官方维护的Helm chart，减少维护成本
2. 标准化的部署流程和最佳实践
3. 简化的升级和回滚能力
4. 更灵活的配置选项
5. 更好的资源管理

## 注意事项

- 确保NFS服务器上存在对应的目录结构
- 如果使用私有Docker仓库，请确保Docker配置文件包含正确的认证信息
- 部署后，可能需要几分钟时间让所有服务启动完成
- 部署NFS
  ```bash
  kubectl create namespace nfs
  helm repo add nfs-ganesha-server-and-external-provisioner https://kubernetes-sigs.github.io/nfs-ganesha-server-and-external-provisioner
  helm upgrade --install -n nfs in-cluster nfs-ganesha-server-and-external-provisioner/nfs-server-provisioner --set 'storageClass.mountOptions={vers=4.1}' --set persistence.enabled=true --set persistence.size=8Gi
  ```

  python3 helm-deploy.py --tenant-id your-tenant-id --config path/to/config.json --install
  python3 helm-deploy.py --tenant-id your-tenant-id --config path/to/config.json --uninstall
  python3 helm-deploy.py --tenant-id your-tenant-id --config path/to/config.json --new-site
  python3 helm-deploy.py --tenant-id your-tenant-id --config path/to/config.json --migrate