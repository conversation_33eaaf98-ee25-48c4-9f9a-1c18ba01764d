apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${TENANT_ID}-nginx
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-nginx
    tenant: ${TENANT_ID}
spec:
  replicas: ${NGINX_REPLICAS:-1}
  selector:
    matchLabels:
      app: ${TENANT_ID}-nginx
      tenant: ${TENANT_ID}
  template:
    metadata:
      labels:
        app: ${TENANT_ID}-nginx
        tenant: ${TENANT_ID}
    spec:
      containers:
      - name: nginx
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["nginx-entrypoint.sh"]
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        resources:
          requests:
            memory: "${NGINX_MEMORY_REQUEST:-256Mi}"
            cpu: "${NGINX_CPU_REQUEST:-100m}"
          limits:
            memory: "${NGINX_MEMORY_LIMIT:-512Mi}"
            cpu: "${NGINX_CPU_LIMIT:-200m}"
        ports:
        - containerPort: 8080
          name: http
        volumeMounts:
        - name: sites-dir
          mountPath: /home/<USER>/lemon-bench/sites
        - name: assets-dir
          mountPath: /home/<USER>/lemon-bench/sites/assets
      volumes:
      - name: sites-dir
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-sites-pvc
      - name: assets-dir
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-assets-pvc
