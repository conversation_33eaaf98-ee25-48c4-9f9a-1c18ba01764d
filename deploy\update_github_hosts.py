#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Host更新工具
为中国大陆用户优化GitHub访问，修改hosts文件来确保正确的GitHub IP解析。
请以管理员/root权限运行此脚本。
"""

import os
import sys
import platform
import subprocess
import socket
import requests
import time
import re

def get_github_ips():
    """
    获取GitHub相关域名的最佳IP地址
    """
    github_domains = [
        'github.com',
        'api.github.com',
        'raw.githubusercontent.com',
        'codeload.github.com',
        'github.global.ssl.fastly.net',
        'assets-cdn.github.com',
        'github.githubassets.com'
    ]
    
    ip_dict = {}
    
    try:
        # 从公共DNS服务获取可用IP
        print("正在获取GitHub域名的IP地址...")
        for domain in github_domains:
            try:
                # 尝试使用阿里DNS服务解析
                response = requests.get(f"https://*********/resolve?name={domain}&type=A", timeout=5)
                data = response.json()
                if "Answer" in data:
                    for answer in data["Answer"]:
                        if answer["type"] == 1:  # A记录
                            ip_dict[domain] = answer["data"]
                            print(f"已找到 {domain} 的IP: {answer['data']}")
                            break
            except Exception as e:
                print(f"无法从DNS服务获取 {domain} 的IP: {e}")
                
            # 如果上面的方法失败，尝试直接解析
            if domain not in ip_dict:
                try:
                    ip = socket.gethostbyname(domain)
                    ip_dict[domain] = ip
                    print(f"已找到 {domain} 的IP: {ip}")
                except socket.gaierror:
                    print(f"无法解析 {domain} 的IP地址")
    
    except Exception as e:
        print(f"获取GitHub IP时出错: {e}")
    
    # 如果没有获取到任何IP，使用备选IP
    if not ip_dict:
        print("使用备选IP地址...")
        ip_dict = {
            'github.com': '**************',
            'api.github.com': '**************',
            'raw.githubusercontent.com': '***************',
            'codeload.github.com': '**************',
            'github.global.ssl.fastly.net': '**************',
            'assets-cdn.github.com': '***************',
            'github.githubassets.com': '***************'
        }
    
    return ip_dict

def check_admin_privileges():
    """
    检查是否具有管理员权限
    """
    if platform.system() == 'Windows':
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    else:
        return os.geteuid() == 0

def backup_hosts_file(hosts_path):
    """
    备份hosts文件
    """
    backup_path = f"{hosts_path}.bak.{int(time.time())}"
    try:
        with open(hosts_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        print(f"已备份hosts文件到 {backup_path}")
        return True
    except Exception as e:
        print(f"备份hosts文件失败: {e}")
        return False

def update_hosts_file(github_ips):
    """
    更新hosts文件以添加或更新GitHub域名的IP地址
    """
    if platform.system() == 'Windows':
        hosts_path = r'C:\Windows\System32\drivers\etc\hosts'
    else:
        hosts_path = '/etc/hosts'
    
    if not check_admin_privileges():
        print("错误: 需要管理员权限来修改hosts文件")
        print("请以管理员/root身份运行此脚本")
        return False
    
    # 备份hosts文件
    if not backup_hosts_file(hosts_path):
        return False
    
    try:
        # 读取当前hosts文件
        with open(hosts_path, 'r') as file:
            hosts_content = file.read()
        
        # 添加GitHub部分的标记
        github_section_start = "# GitHub加速开始 - 由脚本自动添加"
        github_section_end = "# GitHub加速结束"
        
        # 检查是否已存在GitHub部分
        pattern = re.compile(f"{github_section_start}.*?{github_section_end}", re.DOTALL)
        if pattern.search(hosts_content):
            # 移除旧的GitHub部分
            hosts_content = pattern.sub('', hosts_content)
        
        # 构建新的GitHub部分
        github_section = f"\n{github_section_start}\n"
        for domain, ip in github_ips.items():
            github_section += f"{ip} {domain}\n"
        github_section += f"{github_section_end}\n"
        
        # 添加到hosts内容
        hosts_content += github_section
        
        # 写回hosts文件
        with open(hosts_path, 'w') as file:
            file.write(hosts_content)
        
        print("hosts文件已成功更新")
        
        # 刷新DNS缓存
        if platform.system() == 'Windows':
            subprocess.run(['ipconfig', '/flushdns'], check=True)
            print("已刷新DNS缓存")
        elif platform.system() == 'Darwin':  # macOS
            subprocess.run(['dscacheutil', '-flushcache'], check=True)
            subprocess.run(['killall', '-HUP', 'mDNSResponder'], check=True)
            print("已刷新DNS缓存")
        elif platform.system() == 'Linux':
            try:
                subprocess.run(['systemd-resolve', '--flush-caches'], check=True)
                print("已刷新DNS缓存")
            except subprocess.CalledProcessError:
                print("无法刷新DNS缓存，请手动执行")
        
        return True
    
    except Exception as e:
        print(f"更新hosts文件时出错: {e}")
        return False

def test_github_access():
    """
    测试GitHub连接
    """
    print("测试GitHub连接...")
    try:
        response = requests.get("https://github.com", timeout=5)
        if response.status_code == 200:
            print("GitHub连接成功！")
            return True
        else:
            print(f"GitHub连接测试返回状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"GitHub连接测试失败: {e}")
        return False

def main():
    print("GitHub Host更新工具 - 为中国大陆用户优化GitHub访问")
    print("=" * 60)
    
    # 获取GitHub IP地址
    github_ips = get_github_ips()
    if not github_ips:
        print("错误: 无法获取GitHub IP地址")
        sys.exit(1)
    
    # 更新hosts文件
    if update_hosts_file(github_ips):
        print("Hosts文件已更新成功")
        
        # 测试GitHub连接
        if test_github_access():
            print("恭喜！现在您应该能够顺利使用GitHub了")
        else:
            print("GitHub连接测试失败，可能需要等待DNS缓存刷新或检查网络连接")
    else:
        print("Hosts更新失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
