FROM docker.io/frappe/bench:latest

RUN sudo sed -i 's/http:\/\/deb.debian.org/http:\/\/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources
RUN sudo apt update && sudo apt install netcat-openbsd connect-proxy -y
USER frappe
ENV POETRY_PYPI_MIRROR_URL=https://mirrors.aliyun.com/pypi/simple/

RUN mkdir -p /home/<USER>/.pip && \
    echo "[global]" > /home/<USER>/.pip/pip.conf && \
    echo "index-url = https://mirrors.aliyun.com/pypi/simple/" >> /home/<USER>/.pip/pip.conf && \
    echo "trusted-host = mirrors.aliyun.com" >> /home/<USER>/.pip/pip.conf

CMD ["sleep", "inf"]
