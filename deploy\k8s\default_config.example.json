{"deployment_mode": "all-in-one", "tenant_id": "lemon-factory-demo-test", "subdomain": "lemon-factory-demo-test", "docker_registry": "harbor.lemonstudio.tech", "docker_namespace": "lemon-factory", "image_tag": "latest", "domain_suffix": "lemonstudio.tech", "nfs_server": "**************", "nfs_base_path": "/data0/nfs", "backend_replicas": 1, "nginx_replicas": 1, "websocket_replicas": 1, "storage": {"total_size": "20Gi"}, "site_config": {"admin_password": "admin@321LEMON", "db_name": "lemon_factory", "db_password": "dbadmin@123LEMON", "db_root_password": "mariadb@123LEMON", "install_apps": "erpnext,lemon_factory", "initialize": true}, "alicloud_dns": {"enable": true, "access_key_id": "", "access_key_secret": "", "region": "cn-shenzhen", "ingress_ip": "************"}}