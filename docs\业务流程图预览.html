<!DOCTYPE html>
<html>
<head>
    <title>业务流程图预览</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            logLevel: 3,
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true
            }
        });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .mermaid {
            margin: 20px auto;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>业务流程图</h1>
    <div class="mermaid">
flowchart TD
    %% 基础档案区域
    subgraph 基础档案 
        仓库[仓库]
        物料组[物料组]
        物料[物料]
        物料清单[物料清单]
        客户[客户]
        供应商[供应商]
    end
    
    %% 整体流程
    subgraph 整体流程
        %% 销售流程 (紫色)
        销售订单:::销售类 --> 生产计划
        销售订单:::销售类 --> |委外|采购订单委外:::委外类
        销售订单:::销售类 --> 销售出库:::销售类
        销售出库:::销售类 --> 销售退货:::销售类
        销售出库:::销售类 --> 销售发票:::销售类
        销售发票:::销售类 --> 收款:::销售类
        
        %% 生产计划 (橙色/黄色)
        生产计划:::生产类 --> 物料申请
        生产计划:::生产类 --> 生产工单:::生产类
        
        %% 物料申请流程
        物料申请 --> 采购订单
        
        %% 采购流程 (绿色)
        采购订单 --> 采购入库:::采购类
        采购入库:::采购类 --> 采购发票:::采购类
        采购发票:::采购类 --> 付款:::采购类
        采购入库:::采购类 --> 采购退货:::采购类
        
        %% 委外流程 (蓝色)
        采购订单委外:::委外类 --> 委外订单:::委外类
        委外订单:::委外类 --> 委外材料领用:::委外类
        委外材料领用:::委外类 --> 委外入库:::委外类
        
        %% 生产流程 (黄色)
        生产工单:::生产类 --> 生产领料:::生产类
        生产领料:::生产类 --> 生产入库:::生产类
    end
    
    %% 样式定义
    classDef 销售类 fill:#e0ccf5,stroke:#9370db,stroke-width:1px
    classDef 采购类 fill:#c8e6c9,stroke:#4caf50,stroke-width:1px
    classDef 委外类 fill:#bbdefb,stroke:#2196f3,stroke-width:1px
    classDef 生产类 fill:#fff9c4,stroke:#fbc02d,stroke-width:1px
    classDef 基础类 fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    </div>
</body>
</html>
