apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${TENANT_ID}-websocket
  namespace: frappe-lemon
  labels:
    app: ${TENANT_ID}-websocket
    tenant: ${TENANT_ID}
spec:
  replicas: ${WEBSOCKET_REPLICAS:-1}
  selector:
    matchLabels:
      app: ${TENANT_ID}-websocket
      tenant: ${TENANT_ID}
  template:
    metadata:
      labels:
        app: ${TENANT_ID}-websocket
        tenant: ${TENANT_ID}
    spec:
      containers:
      - name: websocket
        image: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe:${IMAGE_TAG:-latest}
        command: ["websocket-entrypoint.sh"]
        envFrom:
        - configMapRef:
            name: ${TENANT_ID}-config
        resources:
          requests:
            memory: "${WEBSOCKET_MEMORY_REQUEST:-256Mi}"
            cpu: "${WEBSOCKET_CPU_REQUEST:-100m}"
          limits:
            memory: "${WEBSOCKET_MEMORY_LIMIT:-512Mi}"
            cpu: "${WEBSOCKET_CPU_LIMIT:-200m}"
        ports:
        - containerPort: 9000
          name: websocket
        volumeMounts:
        - name: sites-dir
          mountPath: /home/<USER>/lemon-bench/sites
      volumes:
      - name: sites-dir
        persistentVolumeClaim:
          claimName: ${TENANT_ID}-sites-pvc
