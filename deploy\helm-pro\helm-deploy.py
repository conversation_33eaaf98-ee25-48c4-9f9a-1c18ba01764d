#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Frappe-Lemon Helm部署脚本

此脚本使用官方Frappe/ERPNext Helm chart部署多租户Frappe应用。
"""

from gettext import install
import os
import sys
import argparse
import json
import base64
import yaml
import subprocess
import tempfile
import shutil
import datetime
from pathlib import Path

# ANSI 颜色代码
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    CYAN = '\033[0;36m'
    GRAY = '\033[0;37m'
    NC = '\033[0m'  # 无颜色


def print_colored(message, color=""):
    """打印彩色文本"""
    color_code = getattr(Colors, color.upper(), "")
    print(f"{color_code}{message}{Colors.NC}")


def execute_command(command, check=True):
    """执行命令并返回结果"""
    try:
        print_colored(f"执行命令: {command}", "GRAY")
        result = subprocess.run(command, shell=True, check=check, text=True, capture_output=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print_colored(f"命令执行失败: {e}", "RED")
        if e.stderr:
            print(e.stderr)
        return False


class HelmDeployer:
    """
    Helm部署器类，负责使用官方Helm chart部署Frappe/ERPNext

    提供以下主要功能：
    1. 部署完整的Frappe/ERPNext应用
    2. 生成并应用特定的Kubernetes资源（站点创建任务、迁移任务、Ingress等）
    3. 管理租户级别的配置和资源
    """

    def __init__(self):
        """初始化默认值和参数解析"""
        # 设置默认值
        self.tenant_id = ""
        self.subdomain = ""
        self.docker_registry = "harbor.lemonstudio.tech"
        self.docker_namespace = "lemon-factory"
        self.image_tag = "pro"
        self.domain_suffix = "lemonstudio.tech"
        self.new_app = ""

        # 证书管理配置
        self.use_certbot = True  # 默认使用 certbot
        self.no_tls = False  # 默认使用 TLS

        # 构建时间
        self.build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 脚本位置和路径
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.docker_config_path = os.path.join(self.project_dir, "resources/docker_config.json")
        self.output_dir = ""

        # 存储配置
        self.nfs_server = "*************"
        self.nfs_base_path = "/data1/nfs/frappe-lemon"
        self.storage_size = "20Gi"

        # 站点初始化配置
        self.admin_password = "admin@321LEMON"
        self.db_name = "frappe"
        self.db_password = "dbadmin@123LEMON"
        self.db_root_password = "mariadb@123LEMON"
        self.install_apps = "erpnext,hrms,print_designer,lemon_factory"
        self.redis_queue = "redis://redis-queue-master:6379"
        self.redis_cache = "redis://redis-cache-master:6379"

        # 数据库配置
        self.use_shared_db = False
        self.shared_db_host = "frappe-mariadb.frappe-lemon.svc.cluster.local"
        self.shared_db_port = "3306"
        self.shared_db_user = "root"
        self.shared_db_password = "mariadb@123LEMON"

        # 资源配置
        self.backend_replicas = 1
        self.nginx_replicas = 1
        self.websocket_replicas = 1
        self.values_file = os.path.join(self.output_dir, f"{self.tenant_id}-values.yaml")

        # 解析命令行参数
        self.parse_arguments()

    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description="使用Helm部署Frappe/ERPNext")
        parser.add_argument("--tenant-id", required=True, help="租户ID")
        parser.add_argument("--subdomain", help=f"子域名（默认与租户ID相同）")
        parser.add_argument("--config", help="配置文件路径")
        parser.add_argument("--docker-registry", help=f"Docker仓库地址（默认：{self.docker_registry}）")
        parser.add_argument("--docker-namespace", help=f"Docker命名空间（默认：{self.docker_namespace}）")
        parser.add_argument("--image-tag", help=f"镜像标签（默认：{self.image_tag}）")
        parser.add_argument("--docker-config", help=f"Docker配置文件路径（默认：{self.docker_config_path}）")
        parser.add_argument("--domain-suffix", help=f"域名后缀（默认：{self.domain_suffix}）")
        parser.add_argument("--nfs-server", help=f"NFS服务器地址（默认：{self.nfs_server}）")
        parser.add_argument("--nfs-base-path", help=f"NFS基础路径（默认：{self.nfs_base_path}）")
        parser.add_argument("--storage-size", help=f"存储大小（默认：{self.storage_size}）")
        parser.add_argument("--admin-password", help=f"管理员密码（默认：{self.admin_password}）")
        parser.add_argument("--db-name", help=f"数据库名称（默认：{self.db_name}）")
        parser.add_argument("--db-password", help=f"数据库密码（默认：{self.db_password}）")
        parser.add_argument("--db-root-password", help=f"数据库root密码（默认：{self.db_root_password}）")
        parser.add_argument("--install-apps", help=f"安装应用列表，逗号分隔（默认：{self.install_apps}）")
        parser.add_argument("--output-dir", help="输出目录，用于存储生成的文件")
        parser.add_argument("--new-app", help="新应用名称")

        # 共享数据库选项
        parser.add_argument("--use-shared-db", action="store_true", help="使用共享的MariaDB服务而不是部署独立的数据库")
        parser.add_argument("--shared-db-host", help=f"共享数据库主机（默认：{self.shared_db_host}）")
        parser.add_argument("--shared-db-port", help=f"共享数据库端口（默认：{self.shared_db_port}）")
        parser.add_argument("--shared-db-user", help=f"共享数据库用户（默认：{self.shared_db_user}）")
        parser.add_argument("--shared-db-password", help=f"共享数据库密码（默认：{self.shared_db_password}）")

        # 操作类型
        action_group = parser.add_argument_group('操作类型')
        action_group.add_argument("--template-only", action="store_true", help="仅生成模板而不应用")
        action_group.add_argument("--install", action="store_true", default=True, help="安装Frappe/ERPNext")
        action_group.add_argument("--uninstall", action="store_true", help="卸载指定租户的Frappe/ERPNext部署")
        action_group.add_argument("--new-site", action="store_true", help="创建新站点")
        action_group.add_argument("--migrate", action="store_true", help="迁移站点")
        action_group.add_argument("--add-app", action="store_true", help="安装新应用")
        action_group.add_argument("--uninstall-app", action="store_true", help="卸载应用")
        action_group.add_argument("--remove-app", action="store_true", help="移除应用")

        # TLS配置
        tls_group = parser.add_argument_group('TLS配置')

        # 证书管理选项 - 互斥组
        cert_group = tls_group.add_mutually_exclusive_group()
        cert_group.add_argument("--use-certbot", dest="use_certbot", action="store_true", default=True,
                               help="使用主机上的certbot管理TLS证书 (默认启用)")
        cert_group.add_argument("--no-tls", dest="no_tls", action="store_true", default=False,
                               help="不配置TLS证书，仅使用HTTP")

        # Certbot配置
        tls_group.add_argument('--certbot-host', type=str, default='lemon.lemonstudio.tech',
                             help='运行certbot的主机名')
        tls_group.add_argument('--certbot-email', type=str,
                             help='用于Let\'s Encrypt注册的邮箱地址，不提供则使用无邮箱模式')

        # 通用TLS配置
        tls_group.add_argument('--ingress-tls-secret-name', type=str,
                             help='Ingress TLS证书Secret名称，默认为{tenant_id}-secret-tls')

        # Chart和资源配置
        chart_group = parser.add_argument_group('Chart和资源配置')
        chart_group.add_argument("--update-repo", action="store_false", default=False, help="更新Helm chart仓库")
        chart_group.add_argument("--chart-version", default="7.0.190", help="Helm chart版本")
        chart_group.add_argument("--ingress-name", help="Ingress资源名称")

        args = parser.parse_args()

        # 处理必填项
        self.tenant_id = args.tenant_id
        self.subdomain = args.subdomain or self.tenant_id

        # 处理可选项
        if args.config:
            self.load_config_file(args.config)
        if args.docker_registry:
            self.docker_registry = args.docker_registry
        if args.docker_namespace:
            self.docker_namespace = args.docker_namespace
        if args.image_tag:
            self.image_tag = args.image_tag
        if args.docker_config:
            self.docker_config_path = args.docker_config
        if args.domain_suffix:
            self.domain_suffix = args.domain_suffix
        if args.nfs_server:
            self.nfs_server = args.nfs_server
        if args.nfs_base_path:
            self.nfs_base_path = args.nfs_base_path
        if args.storage_size:
            self.storage_size = args.storage_size
        if args.admin_password:
            self.admin_password = args.admin_password
        if args.db_name:
            self.db_name = args.db_name
        if args.db_password:
            self.db_password = args.db_password
        if args.db_root_password:
            self.db_root_password = args.db_root_password
        if args.install_apps:
            self.install_apps = args.install_apps
        if args.new_app:
            self.new_app = args.new_app

        # 共享数据库配置
        if args.use_shared_db:
            self.use_shared_db = True
        if args.shared_db_host:
            self.shared_db_host = args.shared_db_host
        if args.shared_db_port:
            self.shared_db_port = args.shared_db_port
        if args.shared_db_user:
            self.shared_db_user = args.shared_db_user
        if args.shared_db_password:
            self.shared_db_password = args.shared_db_password

        # 设置输出目录
        if args.output_dir:
            self.output_dir = args.output_dir
        else:
            self.output_dir = os.path.join(self.script_dir, "output", self.tenant_id)

        self.update_repo = args.update_repo

        # 操作类型
        self.template_only = args.template_only
        self.install = args.install
        self.uninstall_only = args.uninstall
        self.new_site_only = args.new_site
        self.migrate_only = args.migrate
        self.add_app_only = args.add_app
        self.uninstall_app_only = args.uninstall_app
        self.remove_app_only = args.remove_app

        # TLS配置
        self.use_certbot = args.use_certbot
        self.no_tls = args.no_tls
        self.certbot_email = args.certbot_email

        # Chart版本
        self.chart_version = args.chart_version
        self.chart_file = None
        self.ingress_name = args.ingress_name or f"{self.tenant_id}-ingress"
        self.ingress_tls_secret_name = args.ingress_tls_secret_name or getattr(self, 'ingress_tls_secret_name', f"{self.tenant_id}-ssl")
        self.values_file = os.path.join(self.output_dir, f"{self.tenant_id}-values.yaml")


        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)

    def load_config_file(self, config_file):
        """从JSON配置文件加载配置"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                print_colored(f"错误: 配置文件不存在: {config_file}", "RED")
                sys.exit(1)

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 更新基本配置
            for key in config:
                setattr(self, key, config[key])

            # 处理站点配置
            if "site_config" in config:
                site_config = config["site_config"]
                if "admin_password" in site_config:
                    self.admin_password = site_config["admin_password"]
                if "db_name" in site_config:
                    self.db_name = site_config["db_name"]
                if "db_password" in site_config:
                    self.db_password = site_config["db_password"]
                if "db_root_password" in site_config:
                    self.db_root_password = site_config["db_root_password"]
                if "install_apps" in site_config:
                    self.install_apps = site_config["install_apps"]

            print_colored(f"已加载配置文件: {config_file}", "CYAN")

        except Exception as e:
            print_colored(f"加载配置文件时出错: {str(e)}", "RED")
            sys.exit(1)

    def get_docker_credentials(self):
        """从Docker配置文件获取认证信息"""
        try:
            if not os.path.exists(self.docker_config_path):
                print_colored(f"警告: Docker配置文件不存在: {self.docker_config_path}", "YELLOW")
                return None, None

            with open(self.docker_config_path, 'r') as f:
                docker_config = json.load(f)

            if "auths" in docker_config and self.docker_registry in docker_config["auths"]:
                auth_data = docker_config["auths"][self.docker_registry]
                if "auth" in auth_data:
                    auth_bytes = base64.b64decode(auth_data["auth"])
                    username, password = auth_bytes.decode('utf-8').split(':', 1)
                    return username, password
                elif "username" in auth_data and "password" in auth_data:
                    return auth_data["username"], auth_data["password"]

            return None, None

        except Exception as e:
            print_colored(f"读取Docker配置时出错: {str(e)}", "RED")
            return None, None

    def generate_values_yaml(self, output_file=None):
        """生成Helm values.yaml文件
        https://raw.githubusercontent.com/frappe/helm/master/erpnext/values.yaml
        Args:
            output_file: 输出文件路径，如果为None则使用默认路径

        Returns:
            str: 生成的values文件路径
        """
        build_time_env = {
            "name": "BUILD_TIME",
            "value": self.build_time
        }
        # 构建values字典 - 适配template功能
        values = {
            "fullnameOverride": f"frappe-{self.tenant_id}",
            "dbHost": self.shared_db_host if self.use_shared_db else f'frappe-{self.tenant_id}-mariadb.frappe-lemon.svc.cluster.local',
            "dbPort": self.shared_db_port if self.use_shared_db else '3306',
            "dbRootUser": self.shared_db_user if self.use_shared_db else 'root',
            "dbRootPassword": self.shared_db_password if self.use_shared_db else self.db_root_password,
            'redis-cache':{
            'enabled': False,
            'host': self.redis_cache},

            'redis-queue':{
            'enabled': False,
            'host': self.redis_queue},
            # 镜像配置
            "image": {
                "repository": f"{self.docker_registry}/{self.docker_namespace}/frappe",
                "tag": self.image_tag,
                "pullPolicy": "Always"
            },

            # 配置项目级别的imagePullSecrets
            "imagePullSecrets": [{
                "name": "harborsecret"
            }],


            # 持久化存储配置
            "persistence": {
                "worker": {
                    "storageClass": "nfs",  # 使用nfs-client存储类或根据您的集群设置更改
                    # "accessModes": ["ReadWriteMany"],
                    # "size": self.storage_size
                }
            },

            # NFS配置（作为单独部分，而不是persistence.worker.nfs下）
            "nfs": {
                "enabled": True,
                "serverIp": self.nfs_server,
                "path": f"{self.nfs_base_path}/{self.tenant_id}"
            },

            # 配置映射
            "jobs": {
                "volumePermissions": {
                    "enabled": True
                }
            },

            "mariadb": {
                "enabled": False
            },
            # # MariaDB配置
            # "mariadb": {
            #     "enabled": not self.use_shared_db,  # 如果使用共享数据库，则禁用内置MariaDB
            #     "auth": {
            #         "rootPassword": self.db_root_password,
            #         "database": self.db_name,
            #         "password": self.db_password
            #     },
            #     "primary": {
            #         "persistence": {
            #             "enabled": True,
            #             "storageClass": "nfs-client",
            #             "size": "8Gi"
            #         }
            #     }
            # },

            # # 外部数据库配置（当use_shared_db=True时启用）
            # "externalDatabase": {
            #     "enabled": self.use_shared_db,  # 如果使用共享数据库，则启用外部数据库配置
            #     "type": "mariadb",
            #     "host": self.shared_db_host,
            #     "port": int(self.shared_db_port),
            #     "user": "root",
            #     "password": self.db_root_password,
            #     "database": f"{self.tenant_id}_{self.db_name}"
            # },


            "nginx": {
                "envVars": [build_time_env],
            },
            "socketio": {
                "envVars": [build_time_env]
            },

            # 为所有 Pod 添加环境变量
            "worker": {
                "gunicorn": {
                    "envVars": [build_time_env],
                    "initContainers": [{
                        "name": "clear-assets",
                        "image": f"{self.docker_registry}/{self.docker_namespace}/frappe",
                        "imagePullPolicy": "Always",
                        "command": ["bash", "-c"],
                        "args": [
                            "echo 'Clearing assets...';",
                            "echo 'frappe.client_cache.delete_value(\"assets_json\", shared=True)' | bench --site all console;",
                            "echo 'Assets cleared.';"
                        ],
                        "env": [
                            {
                                "name": "REDIS_CACHE",
                                "value": f"redis://redis-cache-master:6379"
                            }
                        ]
                    }]
                },
                "scheduler": {
                    "envVars": [build_time_env]
                },
                "default": {
                    "envVars": [build_time_env]
                },
                "long": {
                    "envVars": [build_time_env]
                },
                "short": {
                    "envVars": [build_time_env]
                }
            }
        }

        # 写入values文件
        if not output_file:
            # 如果没有指定输出文件，使用默认路径
            values_file = os.path.join(self.output_dir, f"{self.tenant_id}-values.yaml")
        else:
            values_file = output_file

        # 确保目录存在
        os.makedirs(os.path.dirname(values_file), exist_ok=True)

        with open(values_file, 'w') as f:
            yaml.dump(values, f, default_flow_style=False)

        print_colored(f"已生成Helm values文件: {values_file}", "GREEN")
        return values_file

    def ensure_helm_repo(self):
        """确保Frappe Helm仓库已添加"""
        print_colored("确保Frappe Helm仓库已添加...", "CYAN")
        if not execute_command("helm repo list | grep frappe"):
            print_colored("添加Frappe Helm仓库", "YELLOW")
            if not execute_command("helm repo add frappe https://helm.erpnext.com"):
                print_colored("添加Frappe Helm仓库失败", "RED")
                sys.exit(1)

        # 确保 cert-manager 仓库已添加
        # print_colored("确保 cert-manager Helm仓库已添加...", "CYAN")
        # if not execute_command("helm repo list | grep jetstack"):
        #     print_colored("添加 cert-manager Helm仓库", "YELLOW")
        #     if not execute_command("helm repo add jetstack https://charts.jetstack.io"):
        #         print_colored("添加 cert-manager Helm仓库失败", "RED")
                # sys.exit(1)

        print_colored("更新Helm仓库", "CYAN")
        if not execute_command("helm repo update"):
            print_colored("更新Helm仓库失败", "RED")
            sys.exit(1)

        return True

    # cert-manager相关代码已删除，改为使用certbot

    def setup_certbot(self, domain=None):
        """在主机上使用certbot设置证书"""
        # 如果不使用certbot或指定了no_tls，则跳过
        if not self.use_certbot or self.no_tls:
            print_colored("跳过certbot证书设置（未启用）", "YELLOW")
            return True

        if domain is None:
            domain = f"{self.subdomain}.{self.domain_suffix}"

        # 判断域名后缀是否为 lemonstudio.tech
        if self.domain_suffix.lower() == "lemonstudio.tech":
            print_colored(f"域名 {domain} 使用lemonstudio.tech后缀，跳过certbot配置", "YELLOW")
            print_colored("假设主机已配置有 *.lemonstudio.tech 的通配符证书", "YELLOW")
            return True

        print_colored(f"为域名 {domain} 设置certbot证书...", "CYAN")

        # 构建certbot命令，直接在本地执行
        certbot_cmd = f'sudo certbot --nginx -d {domain}'

        # 如果提供了邮箱，则使用；否则跳过邮箱输入
        if hasattr(self, 'certbot_email') and self.certbot_email:
            certbot_cmd += f' --email {self.certbot_email}'
        else:
            certbot_cmd += ' --register-unsafely-without-email'

        # 自动同意条款
        certbot_cmd += ' --agree-tos --non-interactive'

        # 执行certbot命令
        if not execute_command(certbot_cmd):
            print_colored(f"certbot为域名 {domain} 设置证书失败", "RED")
            print_colored("请确保主机上已安装certbot，且域名已正确解析到该主机", "RED")
            return False

        print_colored(f"certbot证书已成功设置，域名 {domain} 现在可以通过HTTPS访问", "GREEN")
        return True

    def create_namespace(self):
        """创建Kubernetes命名空间"""
        namespace = "frappe-lemon"
        print_colored(f"确保命名空间 {namespace} 存在...", "CYAN")

        # 检查命名空间是否存在
        result = subprocess.run(["kubectl", "get", "namespace", namespace], capture_output=True, text=True)
        if result.returncode != 0:
            # 创建命名空间
            execute_command(f"kubectl create namespace {namespace}")
            print_colored(f"已创建命名空间: {namespace}", "GREEN")
        else:
            print_colored(f"命名空间 {namespace} 已存在", "CYAN")

    def _generate_template(self, template_type, template_name, set_values=None, output_filename=None):
        """通用模板生成函数

        Args:
            template_type: 模板类型，如 'job-create-site', 'job-migrate-site', 'ingress'
            template_name: 模板名称，用于日志显示
            set_values: 额外的--set参数字典
            output_filename: 输出文件名，默认使用template_type

        Returns:
            str: 生成的模板文件路径，失败时返回None
        """
        print_colored(f"生成{template_name}模板...", "CYAN")

        # 构建Helm命令
        release_name = f"frappe-{self.tenant_id}"
        namespace = "frappe-lemon"

        # 基本命令和参数
        template_path = f"templates/{template_type}.yaml"
        cmd_args = [
            "helm", "template", release_name,
            "-n", namespace,
            self.chart_file,
            "-f", self.values_file,
            "-s", template_path,
            "-f", os.path.join(self.script_dir, template_path)
        ]

        # 添加额外的set参数
        if set_values:
            for key, value in set_values.items():
                if isinstance(value, (dict, list)):
                    value = f"'{json.dumps(value)}'"
                cmd_args.extend(["--set", f"{key}={value}"])

        # 输出文件路径
        output_filename = output_filename or template_type
        output_file = os.path.join(self.output_dir, f"{output_filename}.yaml")

        # 执行命令并将输出写入文件
        try:
            print_colored(f"执行命令: {' '.join(cmd_args)}", "GRAY")
            result = subprocess.run(cmd_args, check=True, text=True, capture_output=True)
            with open(output_file, 'w') as f:
                f.write(result.stdout)
            print_colored(f"已生成{template_name}模板: {output_file}", "GREEN")
            return output_file
        except subprocess.CalledProcessError as e:
            print_colored(f"生成{template_name}模板失败: {e}", "RED")
            if e.stderr:
                print(e.stderr)
            return None

    def template_new_site(self):
        """生成创建站点的任务模板"""
        site_name = f"{self.subdomain}.{self.domain_suffix}"
        apps_list = ",".join([app.strip() for app in self.install_apps.split(',')])

        set_values = {
            "jobs.createSite.siteName": site_name,
            "jobs.createSite.adminPassword": self.admin_password,
            f"jobs.createSite.installApps": "{" + apps_list + "}"
        }

        return self._generate_template(
            template_type="job-create-site",
            template_name="站点创建任务",
            set_values=set_values
        )

    def template_new_app(self):
        """生成创建站点的任务模板"""
        site_name = f"{self.subdomain}.{self.domain_suffix}"
        set_values = {
            "jobs.configure.args": "{" + f"bench --site {site_name} install-app {self.new_app}" +"}"
        }

        return self._generate_template(
            template_type="job-configure-bench",
            template_name="站点创建任务",
            set_values=set_values
        )

    def template_uninstall_app(self):
        """生成创建站点的任务模板"""
        site_name = f"{self.subdomain}.{self.domain_suffix}"
        set_values = {
            "jobs.configure.args": "{" + f"bench --site {site_name} uninstall-app {self.new_app}" +"}"
        }

        return self._generate_template(
            template_type="job-configure-bench",
            template_name="站点创建任务",
            set_values=set_values
        )

    def template_remove_app(self):
        """生成创建站点的任务模板"""
        set_values = {
            "jobs.configure.args": "{" + f"bench remove-app {self.new_app}" +"}"
        }

        return self._generate_template(
            template_type="job-configure-bench",
            template_name="站点创建任务",
            set_values=set_values
        )

    def template_migrate(self):
        """生成迁移站点的任务模板"""
        site_name = f"{self.subdomain}.{self.domain_suffix}"

        set_values = {
            "jobs.migrate.siteName": site_name
        }

        return self._generate_template(
            template_type="job-migrate-site",
            template_name="站点迁移任务",
            set_values=set_values
        )

    def template_pv(self):
        """生成PersistentVolume资源模板"""
        set_values = {
            "metadata.name": f"frappe-{self.tenant_id}-pv",
            "nfs.server": self.nfs_server,
            "nfs.basePath": f"{self.nfs_base_path}/{self.tenant_id}"
        }
        return self._generate_template(
            template_type="pv",
            template_name="PersistentVolume",
            set_values=set_values
        )

    def template_ingress(self):
        """生成Ingress资源模板并配置独立的TLS证书"""
        site_name = f"{self.subdomain}.{self.domain_suffix}"

        # 生成独立的Ingress名称
        if not hasattr(self, 'ingress_name') or not self.ingress_name:
            self.ingress_name = f"{self.tenant_id}-ingress"

        # 基础 ingress 设置
        set_values = {
            "image.tag": self.image_tag,
            "ingress.hosts[0].host": site_name,
            # "ingress.tls[0].hosts[0]": site_name,
            "ingress.ingressName": self.ingress_name,
            # "ingress.tls[0].secretName": self.ingress_tls_secret_name,
            # 固定注解
            "ingress.annotations.kubernetes\\.io/ingress\\.class": "nginx",
            # "ingress.annotations.nginx\\.ingress\\.kubernetes\\.io/ssl-redirect": '\"true\"',
            "ingress.annotations.nginx\\.ingress\\.kubernetes\\.io/proxy-body-size": "50m"
        }

        # 设置TLS相关配置
        if self.no_tls:
            # 如果指定不使用TLS，则移除TLS相关配置
            print_colored("配置Ingress为仅使用HTTP模式（无TLS）", "YELLOW")
            # 从设置中移除TLS配置
            set_values.pop("ingress.tls[0].hosts[0]", None)
            set_values.pop("ingress.tls[0].secretName", None)
        elif self.use_certbot:
            # 使用certbot时，仅配置basic TLS，不添加额外注解
            print_colored("配置Ingress使用certbot管理的TLS证书（主机层）", "CYAN")

        return self._generate_template(
            template_type="ingress",
            template_name="Ingress资源",
            set_values=set_values
        )

    def template_configure_bench(self):
        """生成配置Bench的任务模板"""
        site_name = f"{self.subdomain}.{self.domain_suffix}"
        set_values = {
            "envVars": [
                {
                    "name": "SITE_NAME",
                    "value": site_name
                }
            ]}
        return self._generate_template(
            template_type="job-configure-bench",
            template_name="配置Bench任务",
            set_values=set_values
        )

    def new_site(self):
        """创建新站点"""
        output_file = self.template_new_site()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用站点创建任务到Kubernetes...", "CYAN")
            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("站点创建任务已提交到Kubernetes", "GREEN")
                return True
            else:
                print_colored("站点创建任务提交失败", "RED")
                return False
        return False

    def add_new_app(self):
        """安装新应用"""
        output_file = self.template_new_app()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用站点创建任务到Kubernetes...", "CYAN")
            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("站点创建任务已提交到Kubernetes", "GREEN")
                return True
            else:
                print_colored("站点创建任务提交失败", "RED")
                return False
        return False

    def uninstall_app(self):
        """安装新应用"""
        output_file = self.template_uninstall_app()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用站点创建任务到Kubernetes...", "CYAN")
            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("站点创建任务已提交到Kubernetes", "GREEN")
                return True
            else:
                print_colored("站点创建任务提交失败", "RED")
                return False
        return False

    def remove_app(self):
        """安装新应用"""
        output_file = self.template_remove_app()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用站点创建任务到Kubernetes...", "CYAN")
            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("站点创建任务已提交到Kubernetes", "GREEN")
                return True
            else:
                print_colored("站点创建任务提交失败", "RED")
                return False
        return False

    def upgrade(self):
        """升级站点"""
        output_file = self.template_configure_bench()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用配置Bench任务到Kubernetes...", "CYAN")
             # 从 YAML 文件中提取真正的 job 名称
            job_name = ""
            try:
                import yaml
                with open(output_file, 'r') as f:
                    job_yaml = yaml.safe_load(f)
                    if job_yaml and 'metadata' in job_yaml and 'name' in job_yaml['metadata']:
                        job_name = job_yaml['metadata']['name']
                        print_colored(f"从 YAML 提取的实际Job名称: {job_name}", "CYAN")
                    else:
                        print_colored("无法从 YAML 文件中提取 Job 名称", "YELLOW")
                        job_name = f"migrate-{self.tenant_id}" # 默认名称作为后备
            except Exception as e:
                print_colored(f"解析 YAML 文件时出错: {e}", "YELLOW")
                job_name = f"migrate-{self.tenant_id}" # 默认名称作为后备

            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("配置Bench任务已提交到Kubernetes", "GREEN")
                # 等待迁移任务完成
                print_colored(f"等待迁移任务 {job_name} 完成...", "CYAN")
                if execute_command(f"kubectl wait --for=condition=complete job/{job_name} -n frappe-lemon --timeout=300s"):
                    print_colored("迁移任务已成功完成", "GREEN")
                    return True
                else:
                    print_colored("迁移任务执行超时或失败，请检查日志", "RED")
                    return False
            else:
                print_colored("配置Bench任务提交失败", "RED")
                return False
        return False

    def migrate(self):
        """执行站点迁移并等待完成"""
        output_file = self.template_migrate()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用站点迁移任务到Kubernetes...", "CYAN")

            # 从 YAML 文件中提取真正的 job 名称
            job_name = ""
            try:
                import yaml
                with open(output_file, 'r') as f:
                    job_yaml = yaml.safe_load(f)
                    if job_yaml and 'metadata' in job_yaml and 'name' in job_yaml['metadata']:
                        job_name = job_yaml['metadata']['name']
                        print_colored(f"从 YAML 提取的实际Job名称: {job_name}", "CYAN")
                    else:
                        print_colored("无法从 YAML 文件中提取 Job 名称", "YELLOW")
                        job_name = f"migrate-{self.tenant_id}" # 默认名称作为后备
            except Exception as e:
                print_colored(f"解析 YAML 文件时出错: {e}", "YELLOW")
                job_name = f"migrate-{self.tenant_id}" # 默认名称作为后备

            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("站点迁移任务已提交到Kubernetes", "GREEN")

                # 等待迁移任务完成
                print_colored(f"等待迁移任务 {job_name} 完成...", "CYAN")
                if execute_command(f"kubectl wait --for=condition=complete job/{job_name} -n frappe-lemon --timeout=300s"):
                    print_colored("迁移任务已成功完成", "GREEN")
                    return True
                else:
                    print_colored("迁移任务执行超时或失败，请检查日志", "RED")
                    return False
            else:
                print_colored("站点迁移任务提交失败", "RED")
                return False
        return False

    def create_ingress(self):
        """创建Ingress资源"""
        output_file = self.template_ingress()
        if output_file and os.path.exists(output_file) and not self.template_only:
            print_colored("应用Ingress资源到Kubernetes...", "CYAN")
            if execute_command(f"kubectl -n frappe-lemon apply -f {output_file}"):
                print_colored("Ingress资源已提交到Kubernetes", "GREEN")
                return True
            else:
                print_colored("Ingress资源提交失败", "RED")
                return False
        return False

    def create_pv(self):
        # 先尝试使用template_pv方法生成PV配置

        template_path = os.path.join(self.script_dir, "templates", "pv.yaml")
        if os.path.exists(template_path):
            # 使用yaml解析模板内容
            with open(template_path) as f:
                pv_config = yaml.safe_load(f)

            # 使用yaml方式修改配置
            pv_name = f"frappe-{self.tenant_id}-pv"
            nfs_path = f"{self.nfs_base_path}/{self.tenant_id}"

            # 修改yaml中的值
            pv_config["metadata"]["name"] = pv_name
            pv_config["spec"]["nfs"]["path"] = nfs_path
            pv_config["spec"]["nfs"]["server"] = self.nfs_server

            # 将修改后的内容写入输出文件
            output_file = os.path.join(self.output_dir, "pv.yaml")
            with open(output_file, 'w') as f:
                yaml.safe_dump(pv_config, f, default_flow_style=False)
            print_colored(f"已从本地模板生成PersistentVolume配置: {output_file}", "GREEN")

            # 确保NFS路径存在
            if output_file and os.path.exists(output_file) and not self.template_only:
                # 先检查PV是否已存在
                check_pv_cmd = f"kubectl get pv {pv_name} --ignore-not-found -o name"
                result = subprocess.run(check_pv_cmd, shell=True, text=True, capture_output=True)
                if result.stdout.strip():
                    print_colored(f"PersistentVolume {pv_name} 已存在，跳过创建", "YELLOW")
                    return True

                # PV不存在，创建相关目录和资源
                nfs_path = pv_config["spec"]["nfs"]["path"]
                if not os.path.exists(nfs_path):
                    os.makedirs(nfs_path, mode=0o777, exist_ok=True)
                print_colored("应用PersistentVolume到Kubernetes...", "CYAN")
                if execute_command(f"kubectl apply -f {output_file}"):
                    print_colored("PersistentVolume已提交到Kubernetes", "GREEN")
                    return True
                else:
                    print_colored("PersistentVolume提交失败", "RED")
                    return False
        return False

    def get_or_download_chart(self):
        """获取或下载Helm Chart包"""
        self.chart_file = os.path.join(self.script_dir, f"erpnext-{self.chart_version}.tgz")

        # 如果Chart文件不存在，则下载
        if not os.path.exists(self.chart_file):
            print_colored(f"下载ERPNext Helm Chart版本 {self.chart_version}...", "CYAN")
            cmd = f"helm pull frappe/erpnext --version {self.chart_version} --destination {self.script_dir}"
            if not execute_command(cmd):
                print_colored("下载Chart失败，使用最新版本", "YELLOW")
                cmd = f"helm pull frappe/erpnext --destination {self.script_dir}"
                if not execute_command(cmd):
                    print_colored("下载Chart失败", "RED")
                    sys.exit(1)
                # 获取下载的文件名
                for file in os.listdir(self.script_dir):
                    if file.startswith("erpnext-") and file.endswith(".tgz"):
                        self.chart_file = os.path.join(self.script_dir, file)
                        break

        return self.chart_file

    def deploy(self):
        """部署Frappe/ERPNext使用Helm"""
        # 如果指定了特定操作，则仅执行该操作

        set_values = {
            "dbHost": self.shared_db_host if self.use_shared_db else f'frappe-{self.tenant_id}-mariadb.frappe-lemon.svc.cluster.local',
            "dbPort": self.shared_db_port if self.use_shared_db else '3306',
            "dbUser": self.shared_db_user if self.use_shared_db else 'root',
            "dbRootPassword": self.shared_db_password if self.use_shared_db else self.db_root_password,
            "imagePullSecrets[0].name": "harborsecret",
            "image.repository": f"{self.docker_registry}/{self.docker_namespace}/frappe",
            "image.tag": self.image_tag,
            "persistence.worker.storageClass": "nfs"
        }
        # 确保Helm仓库已添加
        if self.update_repo:
            self.ensure_helm_repo()

        # 创建命名空间
        self.create_namespace()
        # 获取或下载Helm Chart
        self.get_or_download_chart()
        self.values_file = self.generate_values_yaml(self.values_file)

        # 部署命令
        release_name = f"frappe-{self.tenant_id}"
        helm_cmd = f"helm upgrade --install {release_name} {self.chart_file} --namespace frappe-lemon -f {self.values_file} --wait --timeout 100s "
        for key, value in set_values.items():
            helm_cmd += f" --set {key}={value} "
        if self.add_app_only:
            if self.new_app:
                self.add_new_app()
            return
        if self.uninstall_app_only:
            if self.new_app:
                self.uninstall_app()
            return
        if self.remove_app_only:
            if self.new_app:
                self.remove_app()
            return
        if self.new_site_only:
            self.new_site()
            return

        if self.migrate_only:
            # 执行迁移并获取迁移结果
            self.migrate()
            return

        if self.install:
            self.create_ingress()
            self.create_pv()
        if self.template_only:

            print_colored("所有模板已生成完成，存储在 {0} 目录下".format(self.output_dir), "GREEN")
            print_colored("模板已生成，可以使用kubectl命令手动应用模板：", "CYAN")
            print_colored(helm_cmd, "GRAY")
            print_colored("  kubectl -n frappe-lemon apply -f {0}".format(self.output_dir), "GRAY")
            return True

        print_colored(f"开始为租户 {self.tenant_id} 部署Frappe/ERPNext...", "CYAN")


        # 证书管理配置
        if self.use_certbot:
            # 使用certbot管理证书
            print_colored("将使用certbot管理证书，在Ingress创建后配置", "CYAN")
        elif self.no_tls:
            print_colored("按照指定不使用TLS，将仅配置HTTP访问", "YELLOW")

        # 执行 Helm 部署
        print_colored("执行Helm部署命令...", "CYAN")
        if execute_command(helm_cmd):
            print_colored(f"Frappe/ERPNext已成功部署到: {self.subdomain}.{self.domain_suffix}", "GREEN")

            # 创建Ingress并配置TLS
            print_colored("创建Ingress以启用TLS...", "CYAN")
            self.create_ingress()

            # 如果使用certbot，在Ingress创建后配置证书
            if self.use_certbot:
                print_colored("使用certbot配置域名证书...", "CYAN")
                self.setup_certbot()

            # 获取部署状态
            print_colored("\n部署状态:", "CYAN")
            execute_command(f"kubectl get pods -n frappe-lemon -l release={release_name}")

            # 输出访问信息
            print_colored("\n访问信息:", "GREEN")
            print_colored(f"网站地址: https://{self.subdomain}.{self.domain_suffix}", "GREEN")
            print_colored(f"管理员账号: Administrator", "GREEN")
            print_colored(f"管理员密码: {self.admin_password}", "GREEN")
        else:
            print_colored("部署失败，请检查错误信息。", "RED")

    def uninstall(self):
        """卸载Frappe/ERPNext部署"""
        print_colored(f"开始卸载租户 {self.tenant_id} 的Frappe/ERPNext部署...", "CYAN")

        # 卸载命令
        release_name = f"frappe-{self.tenant_id}"
        helm_cmd = f"helm uninstall {release_name} --namespace frappe-lemon"

        # 确认卸载
        print_colored(f"警告: 即将卸载 {release_name}，这将删除所有相关资源和数据", "YELLOW")
        print_colored("执行Helm卸载命令...", "CYAN")

        if execute_command(helm_cmd):
            print_colored(f"Frappe/ERPNext已成功卸载: {release_name}", "GREEN")
            return True
        else:
            print_colored(f"卸载失败: {release_name}", "RED")
            return False

if __name__ == "__main__":
    deployer = HelmDeployer()
    if getattr(deployer, 'uninstall_only', False):
        deployer.uninstall()
    else:
        deployer.deploy()