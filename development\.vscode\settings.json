{"python.defaultInterpreterPath": "${workspaceFolder}/lemon-bench/env/bin/python", "sqltools.connections": [{"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "172.18.0.4", "port": 3306, "driver": "MariaDB", "name": "1111", "username": "root", "database": "erpnext"}], "git.repositoryScanMaxDepth": 3, "git.scanRepositories": ["/workspace/development/lemon-bench/apps/lemon_factory"]}