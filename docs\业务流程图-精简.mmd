flowchart LR
    %% 设置布局方向
    
    %% 基础档案区域
    subgraph 基础档案 [基础档案]
        direction TB
        仓库(仓库)
        物料组(物料组)
        物料(物料)
        物料清单(物料清单)
        客户(客户)
        供应商(供应商)
    end
    
    %% 整体流程区域划分
    subgraph 销售流程 [销售流程]
        direction TB
        销售订单[销售订单]:::销售类
        销售出库[销售出库]:::销售类
        销售退货[销售退货]:::销售类
        销售发票[销售发票]:::销售类
        收款[收款]:::销售类
        
        销售订单 --> 销售出库
        销售出库 --> 销售退货
        销售出库 --> 销售发票
        销售发票 --> 收款
        销售退货 --> 销售发票
    end
    
    subgraph 生产流程 [生产流程]
        direction TB
        生产计划[生产计划MRP]:::生产类
        物料申请[物料申请]
        生产工单[生产工单]:::生产类
        生产领料[生产领料]:::生产类
        生产入库[生产入库]:::生产类
        
        生产计划 --> 物料申请
        生产计划 --> 生产工单
        生产工单 --> 生产领料
        生产领料 --> 生产入库
    end
    
    subgraph 采购流程 [采购流程]
        direction TB
        采购订单[采购订单]
        采购入库[采购入库]:::采购类
        采购发票[采购发票]:::采购类
        采购退货[采购退货]:::采购类
        付款[付款]:::采购类
        
        采购订单 --> 采购入库
        采购入库 --> 采购发票
        采购入库 --> 采购退货
        采购退货 --> 采购发票
        采购发票 --> 付款
    end
    
    subgraph 委外流程 [委外流程]
        direction TB
        采购订单委外[采购订单委外]:::委外类
        委外订单[委外订单]:::委外类
        委外材料调拨[委外材料调拨]:::委外类
        委外入库[委外入库]:::委外类
        
        采购订单委外 --> 委外订单
        委外订单 --> 委外材料调拨
        委外材料调拨 --> 委外入库
    end
    
    %% 流程间连接
    销售订单 --> 生产计划
    销售订单 --> |委外|采购订单委外
    物料申请 --> 采购订单
    委外订单 --> 采购入库
    
    %% 布局优化
    %% 排列顺序
    销售流程 --- 生产流程 --- 采购流程 --- 委外流程
    
    %% 基础档案与主流程连接
    基础档案 --- 销售流程
    
    %% 样式定义
    classDef 销售类 fill:#e0ccf5,stroke:#9370db,stroke-width:1px
    classDef 采购类 fill:#c8e6c9,stroke:#4caf50,stroke-width:1px
    classDef 委外类 fill:#bbdefb,stroke:#2196f3,stroke-width:1px
    classDef 生产类 fill:#fff9c4,stroke:#fbc02d,stroke-width:1px
    classDef 基础类 fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px