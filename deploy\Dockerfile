ARG DOCKER_REGISTRY=harbor.lemonstudio.tech
ARG DOCKER_NAMESPACE=lemon-factory
ARG MAIN_VERSION=version-15
ARG TAG=latest
FROM ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe-factory:${TAG} AS factory


USER frappe

ARG CUSTOM_APPS=
SHELL [ "/bin/bash" , "-c" ]
RUN \
  cd /home/<USER>/frappe-bench \
  && if [ -n "${CUSTOM_APPS}" ]; then \
  echo $CUSTOM_APPS | while read -r line; \
  do \
    app=(${line//,/ }); \
    bench get-app --branch=${app[2]:-"master"} --resolve-deps ${app[0]} ${app[1]}; \
  done \
  fi \
  && find apps -mindepth 1 -path "*/.git" | xargs rm -fr

FROM ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/frappe-base:latest as custom


COPY resources/nginx-template.conf /templates/nginx/frappe.conf.template
COPY resources/entrypoints /usr/local/bin
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN cd /usr/local/bin \
  && chmod 755 \
  backend-entrypoint.sh \
  configurator-entrypoint.sh \
  nginx-entrypoint.sh \
  websocket-entrypoint.sh

USER frappe

COPY --from=factory --chown=frappe:frappe /home/<USER>/frappe-bench /home/<USER>/frappe-bench

WORKDIR /home/<USER>/frappe-bench

VOLUME [ \
  "/home/<USER>/frappe-bench/sites", \
  "/home/<USER>/frappe-bench/sites/assets", \
  "/home/<USER>/frappe-bench/logs" \
]

CMD [ "backend-entrypoint.sh" ]